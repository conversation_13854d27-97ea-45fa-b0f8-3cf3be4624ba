#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows右键菜单程序：将文件名中的空格替换为下划线
作者：AI助手
功能：批量重命名选中的文件，将文件名中的空格替换为下划线
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import argparse
from pathlib import Path


def replace_spaces_in_filename(file_path):
    """
    将单个文件名中的空格替换为下划线
    
    Args:
        file_path (str): 文件的完整路径
        
    Returns:
        tuple: (是否成功, 原文件名, 新文件名, 错误信息)
    """
    try:
        path_obj = Path(file_path)
        
        # 检查文件是否存在
        if not path_obj.exists():
            return False, file_path, "", f"文件不存在: {file_path}"
        
        # 获取文件名和扩展名
        original_name = path_obj.name
        
        # 如果文件名中没有空格，跳过
        if ' ' not in original_name:
            return True, original_name, original_name, "文件名中没有空格，无需修改"
        
        # 替换空格为下划线
        new_name = original_name.replace(' ', '_')
        
        # 构建新的完整路径
        new_path = path_obj.parent / new_name
        
        # 检查新文件名是否已存在
        if new_path.exists():
            return False, original_name, new_name, f"目标文件已存在: {new_name}"
        
        # 重命名文件
        path_obj.rename(new_path)
        
        return True, original_name, new_name, "重命名成功"
        
    except PermissionError:
        return False, file_path, "", "权限不足，无法重命名文件"
    except Exception as e:
        return False, file_path, "", f"重命名失败: {str(e)}"


def process_files(file_paths, show_gui=True):
    """
    批量处理多个文件
    
    Args:
        file_paths (list): 文件路径列表
        show_gui (bool): 是否显示GUI结果窗口
    """
    results = []
    success_count = 0
    skip_count = 0
    error_count = 0
    
    for file_path in file_paths:
        success, original_name, new_name, message = replace_spaces_in_filename(file_path)
        
        results.append({
            'success': success,
            'original_name': original_name,
            'new_name': new_name,
            'message': message,
            'path': file_path
        })
        
        if success:
            if original_name == new_name:
                skip_count += 1
            else:
                success_count += 1
        else:
            error_count += 1
    
    # 显示结果
    if show_gui:
        show_results_gui(results, success_count, skip_count, error_count)
    else:
        print_results_console(results, success_count, skip_count, error_count)


def show_results_gui(results, success_count, skip_count, error_count):
    """
    在GUI窗口中显示处理结果
    """
    root = tk.Tk()
    root.title("文件重命名结果")
    root.geometry("800x600")
    root.resizable(True, True)
    
    # 创建主框架
    main_frame = tk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 统计信息
    stats_text = f"处理完成！成功: {success_count}, 跳过: {skip_count}, 失败: {error_count}"
    stats_label = tk.Label(main_frame, text=stats_text, font=("Arial", 12, "bold"))
    stats_label.pack(pady=(0, 10))
    
    # 创建滚动文本框显示详细结果
    text_frame = tk.Frame(main_frame)
    text_frame.pack(fill=tk.BOTH, expand=True)
    
    # 文本框和滚动条
    text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
    scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)
    
    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 填充结果内容
    for i, result in enumerate(results, 1):
        status = "✓" if result['success'] else "✗"
        if result['success'] and result['original_name'] == result['new_name']:
            status = "○"  # 跳过的文件
        
        text_widget.insert(tk.END, f"{i:3d}. {status} {result['original_name']}\n")
        
        if result['success'] and result['original_name'] != result['new_name']:
            text_widget.insert(tk.END, f"     → {result['new_name']}\n")
        
        if result['message']:
            text_widget.insert(tk.END, f"     {result['message']}\n")
        
        text_widget.insert(tk.END, "\n")
    
    text_widget.config(state=tk.DISABLED)
    
    # 关闭按钮
    close_button = tk.Button(main_frame, text="关闭", command=root.destroy, 
                           font=("Arial", 10), padx=20)
    close_button.pack(pady=(10, 0))
    
    # 居中显示窗口
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()


def print_results_console(results, success_count, skip_count, error_count):
    """
    在控制台中打印处理结果
    """
    print(f"\n处理完成！成功: {success_count}, 跳过: {skip_count}, 失败: {error_count}\n")
    
    for i, result in enumerate(results, 1):
        status = "✓" if result['success'] else "✗"
        if result['success'] and result['original_name'] == result['new_name']:
            status = "○"
        
        print(f"{i:3d}. {status} {result['original_name']}")
        
        if result['success'] and result['original_name'] != result['new_name']:
            print(f"     → {result['new_name']}")
        
        if result['message']:
            print(f"     {result['message']}")
        
        print()


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='将文件名中的空格替换为下划线')
    parser.add_argument('files', nargs='*', help='要处理的文件路径')
    parser.add_argument('--no-gui', action='store_true', help='不显示GUI，仅在控制台输出结果')
    
    args = parser.parse_args()
    
    # 如果没有提供文件参数，显示使用说明
    if not args.files:
        if not args.no_gui:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showinfo("使用说明", 
                              "请通过右键菜单选择文件使用此程序，\n"
                              "或者在命令行中提供文件路径参数。")
            root.destroy()
        else:
            print("使用方法: python replace_spaces_in_filename.py <文件路径1> [文件路径2] ...")
        return
    
    # 过滤存在的文件
    valid_files = []
    for file_path in args.files:
        if os.path.exists(file_path):
            valid_files.append(file_path)
        else:
            print(f"警告: 文件不存在 - {file_path}")
    
    if not valid_files:
        if not args.no_gui:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("错误", "没有找到有效的文件！")
            root.destroy()
        else:
            print("错误: 没有找到有效的文件！")
        return
    
    # 处理文件
    process_files(valid_files, show_gui=not args.no_gui)


if __name__ == "__main__":
    main()
