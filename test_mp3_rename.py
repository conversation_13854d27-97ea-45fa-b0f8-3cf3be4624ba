#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MP3文件重命名功能
"""
import os

def test_mp3_rename_logic():
    """测试MP3文件重命名逻辑"""
    
    # 模拟不同大小的文件
    test_cases = [
        ("small_song.mp3", 50 * 1024 * 1024, False),      # 50MB，不应该重命名
        ("medium_song.mp3", 200 * 1024 * 1024, False),    # 200MB，不应该重命名
        ("large_song.mp3", 350 * 1024 * 1024, True),      # 350MB，应该重命名
        ("huge_song.mp3", 500 * 1024 * 1024, True),       # 500MB，应该重命名
        ("normal.txt", 400 * 1024 * 1024, True),          # 400MB TXT，应该重命名
        ("document.pdf", 400 * 1024 * 1024, True),        # 400MB PDF，应该重命名
        ("image.gif", 400 * 1024 * 1024, True),           # 400MB GIF，应该重命名
        ("video.mp4", 400 * 1024 * 1024, False),          # 400MB MP4，不应该重命名
    ]
    
    print("测试MP3及其他大文件重命名逻辑:")
    print("=" * 60)
    
    for filename, file_size, should_rename in test_cases:
        file_path = f"/test/{filename}"
        base_filename, ext = os.path.splitext(file_path)
        file_size_mb = file_size / (1024 * 1024)
        
        # 应用重命名逻辑
        new_path = file_path
        changed = False
        
        # 检查是否应该重命名为.zip
        if ext.lower() in ['.pdf', '.doc', '.docx', '.txt', '.gif', '.mp3'] and file_size > 300 * 1024 * 1024:
            new_path = base_filename + '.zip'
            changed = True
        
        # 显示结果
        status = "✓" if changed == should_rename else "✗"
        action = "重命名为.zip" if changed else "保持不变"
        expected = "应该重命名" if should_rename else "不应重命名"
        
        print(f"{status} {filename:<20} ({file_size_mb:6.1f}MB) -> {action} ({expected})")
        
        if changed != should_rename:
            print(f"  ⚠️  预期: {expected}, 实际: {'重命名' if changed else '不重命名'}")
    
    print("\n测试完成!")
    print("\n新增功能说明:")
    print("- 大于300MB的.mp3文件现在会被重命名为.zip扩展名")
    print("- 这有助于识别可能被伪装成音频文件的压缩包")

if __name__ == "__main__":
    test_mp3_rename_logic()
