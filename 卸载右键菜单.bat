@echo off
chcp 65001 >nul
title 卸载文件名空格替换右键菜单

echo ========================================
echo   文件名空格替换工具 - 右键菜单卸载
echo ========================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告：建议以管理员身份运行此卸载程序
    echo 如果卸载失败，请右键选择"以管理员身份运行"
    echo.
)

echo 准备卸载右键菜单项...
echo.
echo 将要删除的菜单项：
echo • 将文件名空格替换为下划线（单文件）
echo • 将文件名空格替换为下划线（多文件选择）
echo.

set /p "CONFIRM=确认卸载？(Y/N): "
if /i not "%CONFIRM%"=="Y" if /i not "%CONFIRM%"=="YES" (
    echo 卸载已取消。
    pause
    exit /b 0
)

echo.
echo 正在生成卸载注册表文件...

REM 生成卸载注册表文件
echo Windows Registry Editor Version 5.00 > "%TEMP%\uninstall_context_menu_temp.reg"
echo. >> "%TEMP%\uninstall_context_menu_temp.reg"
echo ; 卸载右键菜单项：将空格替换为下划线 >> "%TEMP%\uninstall_context_menu_temp.reg"
echo. >> "%TEMP%\uninstall_context_menu_temp.reg"
echo ; 删除单文件右键菜单 >> "%TEMP%\uninstall_context_menu_temp.reg"
echo [-HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces] >> "%TEMP%\uninstall_context_menu_temp.reg"
echo. >> "%TEMP%\uninstall_context_menu_temp.reg"
echo ; 删除多文件右键菜单 >> "%TEMP%\uninstall_context_menu_temp.reg"
echo [-HKEY_CLASSES_ROOT\AllFilesystemObjects\shellex\ContextMenuHandlers\ReplaceSpaces] >> "%TEMP%\uninstall_context_menu_temp.reg"

echo ✓ 卸载注册表文件已生成

echo.
echo 正在卸载右键菜单...

REM 执行卸载注册表文件
regedit /s "%TEMP%\uninstall_context_menu_temp.reg"

if %errorlevel% equ 0 (
    echo ✓ 右键菜单卸载成功！
    
    REM 清理临时文件
    del "%TEMP%\uninstall_context_menu_temp.reg" >nul 2>&1
    
    echo.
    echo ========================================
    echo 卸载完成！
    echo ========================================
    echo.
    echo 右键菜单项已从系统中移除。
    echo 您可能需要重启文件资源管理器或重启电脑才能看到变化。
    echo.
    echo 如需重新安装，请运行"安装右键菜单.bat"
    echo.
) else (
    echo ✗ 右键菜单卸载失败！
    echo.
    echo 可能的原因：
    echo 1. 权限不足 - 请以管理员身份运行
    echo 2. 注册表被保护 - 请检查安全软件设置
    echo 3. 系统限制 - 请检查组策略设置
    echo.
    
    REM 清理临时文件
    del "%TEMP%\uninstall_context_menu_temp.reg" >nul 2>&1
)

pause
