@echo off
chcp 65001 >nul
title 安装文件名空格替换右键菜单

echo ========================================
echo   文件名空格替换工具 - 右键菜单安装
echo ========================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告：建议以管理员身份运行此安装程序
    echo 如果安装失败，请右键选择"以管理员身份运行"
    echo.
)

REM 获取当前目录（去掉末尾反斜杠）
set "CURRENT_DIR=%~dp0"
if "%CURRENT_DIR:~-1%"=="\" set "CURRENT_DIR=%CURRENT_DIR:~0,-1%"
echo 当前安装目录：%CURRENT_DIR%
echo.

REM 检查必要文件是否存在
echo 检查必要文件...
set "MISSING_FILES="

if not exist "%CURRENT_DIR%\replace_spaces_in_filename.py" (
    echo ✗ 缺少文件：replace_spaces_in_filename.py
    set "MISSING_FILES=1"
) else (
    echo ✓ replace_spaces_in_filename.py
)

if not exist "%CURRENT_DIR%\replace_spaces_menu.bat" (
    echo ✗ 缺少文件：replace_spaces_menu.bat
    set "MISSING_FILES=1"
) else (
    echo ✓ replace_spaces_menu.bat
)

if defined MISSING_FILES (
    echo.
    echo 错误：缺少必要文件，无法继续安装！
    pause
    exit /b 1
)

echo.
echo 检查Python环境...

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Python环境正常
    python --version
) else (
    python3 --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ Python3环境正常
        python3 --version
    ) else (
        py --version >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✓ Python Launcher环境正常
            py --version
        ) else (
            echo ✗ 未找到Python环境！
            echo.
            echo 请先安装Python：
            echo 1. 访问 https://www.python.org/
            echo 2. 下载并安装最新版本的Python
            echo 3. 安装时勾选"Add Python to PATH"选项
            echo 4. 重新运行此安装程序
            echo.
            pause
            exit /b 1
        )
    )
)

echo.
echo ========================================
echo 准备安装右键菜单...
echo ========================================
echo.
echo 安装内容：
echo • 单文件右键菜单：将文件名空格替换为下划线
echo • 多文件选择菜单：批量替换文件名空格为下划线
echo.
echo 安装位置：%CURRENT_DIR%
echo.

set /p "CONFIRM=确认安装？(Y/N): "
if /i not "%CONFIRM%"=="Y" if /i not "%CONFIRM%"=="YES" (
    echo 安装已取消。
    pause
    exit /b 0
)

echo.
echo 正在生成注册表文件...

REM 转义路径中的反斜杠
set "ESCAPED_PATH=%CURRENT_DIR:\=\\%"

REM 生成动态注册表文件
echo Windows Registry Editor Version 5.00 > "%TEMP%\install_context_menu_temp.reg"
echo. >> "%TEMP%\install_context_menu_temp.reg"
echo ; 为文件添加右键菜单项：将空格替换为下划线 >> "%TEMP%\install_context_menu_temp.reg"
echo [HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces] >> "%TEMP%\install_context_menu_temp.reg"
echo @="将文件名空格替换为下划线" >> "%TEMP%\install_context_menu_temp.reg"
echo "Icon"="shell32.dll,134" >> "%TEMP%\install_context_menu_temp.reg"
echo. >> "%TEMP%\install_context_menu_temp.reg"
echo [HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces\command] >> "%TEMP%\install_context_menu_temp.reg"
echo @="\"%ESCAPED_PATH%\\replace_spaces_menu.bat\" \"%%1\"" >> "%TEMP%\install_context_menu_temp.reg"
echo. >> "%TEMP%\install_context_menu_temp.reg"
echo ; 为多选文件添加右键菜单项 >> "%TEMP%\install_context_menu_temp.reg"
echo [HKEY_CLASSES_ROOT\AllFilesystemObjects\shellex\ContextMenuHandlers\ReplaceSpaces] >> "%TEMP%\install_context_menu_temp.reg"
echo @="{60254CA5-953B-11CF-8C96-00AA00B8708C}" >> "%TEMP%\install_context_menu_temp.reg"

echo ✓ 注册表文件已生成

echo.
echo 正在安装右键菜单...

REM 执行注册表文件
regedit /s "%TEMP%\install_context_menu_temp.reg"

if %errorlevel% equ 0 (
    echo ✓ 右键菜单安装成功！

    REM 清理临时文件
    del "%TEMP%\install_context_menu_temp.reg" >nul 2>&1

    echo.
    echo ========================================
    echo 安装完成！
    echo ========================================
    echo.
    echo 使用方法：
    echo 1. 右键点击任意文件 → 选择"将文件名空格替换为下划线"
    echo 2. 选择多个文件后右键 → 选择"将文件名空格替换为下划线"
    echo.
    echo 注意事项：
    echo • 重命名操作不可撤销，请谨慎使用
    echo • 建议先在测试文件上验证功能
    echo • 如需卸载，请运行此目录下的卸载脚本
    echo.
) else (
    echo ✗ 右键菜单安装失败！
    echo.
    echo 可能的原因：
    echo 1. 权限不足 - 请以管理员身份运行
    echo 2. 注册表被保护 - 请检查安全软件设置
    echo 3. 系统限制 - 请检查组策略设置
    echo.

    REM 清理临时文件
    del "%TEMP%\install_context_menu_temp.reg" >nul 2>&1
)

pause
