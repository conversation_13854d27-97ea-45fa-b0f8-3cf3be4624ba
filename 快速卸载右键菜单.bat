@echo off
chcp 65001 >nul
title 快速卸载右键菜单

echo ========================================
echo   文件名空格替换工具 - 快速卸载
echo ========================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：需要管理员权限！
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo 正在卸载文件名空格替换右键菜单...
echo.

REM 生成卸载注册表文件
(
echo Windows Registry Editor Version 5.00
echo.
echo [-HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces]
) > "%TEMP%\uninstall_menu.reg"

echo 正在移除右键菜单...

REM 执行卸载
regedit.exe /s "%TEMP%\uninstall_menu.reg"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 卸载成功！
    echo ========================================
    echo.
    echo 右键菜单项已移除。
    echo 您可能需要重启文件资源管理器才能看到变化。
) else (
    echo.
    echo 卸载失败！请检查是否有足够的权限。
)

REM 清理临时文件
del "%TEMP%\uninstall_menu.reg" >nul 2>&1

echo.
pause
