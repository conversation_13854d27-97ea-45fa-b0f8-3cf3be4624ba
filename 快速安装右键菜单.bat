@echo off
chcp 65001 >nul
title 快速安装右键菜单

echo ========================================
echo   文件名空格替换工具 - 快速安装
echo ========================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：需要管理员权限！
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo 正在安装文件名空格替换右键菜单...
echo.

REM 获取当前目录（保留末尾反斜杠）
set "CURRENT_DIR=%~dp0"
echo 安装目录：%CURRENT_DIR%

REM 检查必要文件是否存在
if not exist "%CURRENT_DIR%replace_spaces_in_filename.py" (
    echo 错误：找不到 replace_spaces_in_filename.py 文件！
    echo 请确保所有文件都在同一目录中。
    pause
    exit /b 1
)

if not exist "%CURRENT_DIR%replace_spaces_menu.bat" (
    echo 错误：找不到 replace_spaces_menu.bat 文件！
    echo 请确保所有文件都在同一目录中。
    pause
    exit /b 1
)

REM 转义路径中的反斜杠（用于注册表）
set "ESCAPED_PATH=%CURRENT_DIR:\=\\%"
REM 去掉末尾的双反斜杠
if "%ESCAPED_PATH:~-2%"=="\\" set "ESCAPED_PATH=%ESCAPED_PATH:~0,-2%"

echo 正在生成注册表文件...

REM 生成注册表文件
(
echo Windows Registry Editor Version 5.00
echo.
echo [HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces]
echo @="将文件名空格替换为下划线"
echo "Icon"="shell32.dll,134"
echo.
echo [HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces\command]
echo @="\"%ESCAPED_PATH%\\replace_spaces_menu.bat\" \"%%1\""
) > "%TEMP%\install_menu.reg"

echo 正在安装右键菜单...

REM 安装注册表项
regedit.exe /s "%TEMP%\install_menu.reg"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 安装成功！
    echo ========================================
    echo.
    echo 现在您可以：
    echo 1. 右键点击任意文件
    echo 2. 选择"将文件名空格替换为下划线"
    echo.
    echo 注意：如果右键菜单没有立即出现，
    echo 请重启文件资源管理器或重启电脑。
) else (
    echo.
    echo 安装失败！请检查是否有足够的权限。
)

REM 清理临时文件
del "%TEMP%\install_menu.reg" >nul 2>&1

echo.
pause
