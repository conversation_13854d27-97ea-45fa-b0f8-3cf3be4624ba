# 新建文件夹功能说明

## 功能概述

在网络媒体文件夹整理工具的右键菜单中新增了"新建文件夹"功能，用户可以方便地在任意位置创建新文件夹。

## 功能特点

### 1. 多种触发方式

- **文件夹右键菜单**: 在任意文件夹上右键，选择"📁 新建文件夹"
- **空白区域右键菜单**: 在目录树空白区域右键，选择"📁 新建文件夹"
- **根目录操作**: 支持在当前选择的根目录中直接新建文件夹

### 2. 智能命名处理

- **文件名清理**: 自动移除Windows文件系统不支持的非法字符
  - 移除字符: `< > : " / \ | ? *`
  - 清理首尾空格和点号
- **重名处理**: 如果文件夹已存在，自动添加数字后缀
  - 例如: `新文件夹` → `新文件夹(1)` → `新文件夹(2)`

### 3. 操作记录

- **日志记录**: 所有新建文件夹操作都会记录到操作日志中
- **操作追踪**: 包含时间戳、路径等详细信息
- **自动刷新**: 创建完成后自动刷新目录树显示

## 使用方法

### 方法一：文件夹右键菜单

1. 在目录树中找到要创建子文件夹的父文件夹
2. 右键点击该文件夹
3. 选择"📁 新建文件夹"
4. 在弹出的对话框中输入文件夹名称
5. 点击确定完成创建

### 方法二：空白区域右键菜单

1. 在目录树的空白区域右键点击
2. 选择"📁 新建文件夹"
3. 在弹出的对话框中输入文件夹名称
4. 点击确定在根目录中创建文件夹

## 错误处理

### 输入验证
- 空文件夹名: 取消操作
- 非法字符: 自动清理并提示
- 无效名称: 显示错误提示

### 路径验证
- 父目录不存在: 显示错误提示
- 权限不足: 显示系统错误信息

### 异常处理
- 创建失败: 显示详细错误信息
- 系统异常: 记录到日志并提示用户

## 技术实现

### 核心方法

1. **create_new_folder(parent_item)**: 在指定文件夹中新建文件夹
2. **create_new_folder_in_root()**: 在根目录中新建文件夹
3. **clean_filename(filename)**: 清理文件名非法字符
4. **show_empty_area_context_menu(event)**: 显示空白区域右键菜单

### 文件名清理规则

```python
def clean_filename(self, filename):
    """清理文件名，移除非法字符"""
    # Windows 文件名非法字符
    illegal_chars = '<>:"/\\|?*'
    for char in illegal_chars:
        filename = filename.replace(char, '')
    
    # 移除首尾空格和点
    filename = filename.strip(' .')
    
    return filename
```

### 重名处理逻辑

```python
# 检查是否已存在同名文件夹
if os.path.exists(new_folder_path):
    # 生成唯一名称
    counter = 1
    base_name = folder_name
    while os.path.exists(new_folder_path):
        folder_name = f"{base_name}({counter})"
        new_folder_path = os.path.join(parent_path, folder_name)
        counter += 1
```

## 日志示例

```
[14:30:25] ✅ 新建文件夹成功: 测试文件夹
[14:30:35] 文件夹已存在，自动重命名为: 测试文件夹(1)
[14:30:35] ✅ 新建文件夹成功: 测试文件夹(1)
```

## 注意事项

1. **权限要求**: 需要对目标目录有写入权限
2. **路径长度**: Windows系统路径长度限制（通常260字符）
3. **文件系统**: 仅支持Windows文件系统的命名规则
4. **并发操作**: 建议避免同时进行多个文件夹操作

## 更新历史

- **v1.0**: 初始版本，支持基本的新建文件夹功能
- **v1.1**: 添加文件名清理和重名处理
- **v1.2**: 增加空白区域右键菜单支持
