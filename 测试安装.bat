@echo off
chcp 65001 >nul
title 测试安装脚本

echo ========================================
echo   测试安装脚本
echo ========================================
echo.

echo 当前目录：%~dp0
echo.

echo 检查文件是否存在：
if exist "replace_spaces_in_filename.py" (
    echo ✓ replace_spaces_in_filename.py 存在
) else (
    echo ✗ replace_spaces_in_filename.py 不存在
)

if exist "replace_spaces_menu.bat" (
    echo ✓ replace_spaces_menu.bat 存在
) else (
    echo ✗ replace_spaces_menu.bat 不存在
)

echo.
echo 测试路径转义：
set "CURRENT_DIR=%~dp0"
echo 原始路径：%CURRENT_DIR%

set "ESCAPED_PATH=%CURRENT_DIR:\=\\%"
if "%ESCAPED_PATH:~-2%"=="\\" set "ESCAPED_PATH=%ESCAPED_PATH:~0,-2%"
echo 转义后路径：%ESCAPED_PATH%

echo.
echo 测试注册表文件生成：
(
echo Windows Registry Editor Version 5.00
echo.
echo [HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces]
echo @="将文件名空格替换为下划线"
echo "Icon"="shell32.dll,134"
echo.
echo [HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces\command]
echo @="\"%ESCAPED_PATH%\\replace_spaces_menu.bat\" \"%%1\""
) > "%TEMP%\test_install_menu.reg"

echo ✓ 注册表文件已生成到：%TEMP%\test_install_menu.reg

echo.
echo 注册表文件内容：
type "%TEMP%\test_install_menu.reg"

echo.
echo 清理测试文件...
del "%TEMP%\test_install_menu.reg" >nul 2>&1

echo.
echo 测试完成！如果上面的内容看起来正确，
echo 您可以运行"快速安装右键菜单.bat"进行实际安装。
echo.
pause
