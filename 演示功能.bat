@echo off
chcp 65001 >nul
title 文件名空格替换工具演示

echo ========================================
echo   文件名空格替换工具 - 功能演示
echo ========================================
echo.

echo 正在创建演示文件...

REM 创建测试文件
echo 这是测试文件1 > "演示 文件 1.txt"
echo 这是测试文件2 > "另一个 测试 文件.txt"
echo 这是测试文件3 > "包含 多个 空格 的文件.txt"
echo 这是没有空格的文件 > "没有空格的文件.txt"

echo ✓ 已创建以下演示文件：
echo   - 演示 文件 1.txt
echo   - 另一个 测试 文件.txt
echo   - 包含 多个 空格 的文件.txt
echo   - 没有空格的文件.txt
echo.

echo 当前文件列表：
dir /b *演示*.txt *另一个*.txt *包含*.txt *没有空格*.txt 2>nul
echo.

pause

echo 正在演示批量处理功能...
echo.

REM 使用Python脚本处理所有测试文件
python replace_spaces_in_filename.py "演示 文件 1.txt" "另一个 测试 文件.txt" "包含 多个 空格 的文件.txt" "没有空格的文件.txt" --no-gui

echo.
echo 处理后的文件列表：
dir /b *演示*.txt *另一个*.txt *包含*.txt *没有空格*.txt 2>nul
echo.

echo ========================================
echo 演示完成！
echo ========================================
echo.
echo 观察结果：
echo • 包含空格的文件名已被替换为下划线
echo • 没有空格的文件名保持不变
echo • 所有操作都有详细的结果反馈
echo.

echo 现在您可以：
echo 1. 运行"快速安装右键菜单.bat"安装右键菜单
echo 2. 右键点击文件使用"将文件名空格替换为下划线"功能
echo.

set /p "CLEANUP=是否清理演示文件？(Y/N): "
if /i "%CLEANUP%"=="Y" (
    echo 正在清理演示文件...
    del "演示_文件_1.txt" 2>nul
    del "另一个_测试_文件.txt" 2>nul
    del "包含_多个_空格_的文件.txt" 2>nul
    del "没有空格的文件.txt" 2>nul
    echo ✓ 演示文件已清理
)

echo.
echo 演示结束！
pause
