import os
import shutil
from tkinter import Tk
from tkinter.filedialog import askdirectory


def get_earliest_separator_position(file_name):
    """找到所有可能的分隔符位置，并返回最早出现的分隔符位置。"""
    # 优先使用空格作为分隔符
    space_pos = file_name.find(' ')
    if space_pos != -1:
        return space_pos

    # 如果没有空格，则使用其他分隔符
    positions = [
        file_name.find('_'),
        file_name.find('-'),
        file_name.find(','),
        file_name.find('，')
    ]
    positions = [pos for pos in positions if pos != -1]  # 过滤掉未找到的分隔符
    return min(positions) if positions else -1


def organize_files_by_prefix(folder_path):
    """根据文件名前缀整理文件到相应的子文件夹中。"""
    files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]

    for file in files:
        clean_file_name = file  # 保留文件名中的#字符

        pos = get_earliest_separator_position(clean_file_name)
        if pos != -1:
            prefix = clean_file_name[:pos].strip()  # 提取前缀
        else:
            prefix = clean_file_name.rsplit('.', 1)[0].strip()  # 如果没有分隔符，则使用文件名作为前缀

        # 显示提取到的文件夹名
        print(f"文件: '{clean_file_name}' 提取到的文件夹名: '{prefix}'")

        # 构建子文件夹路径
        subfolder_path = os.path.join(folder_path, prefix)

        if not os.path.exists(subfolder_path):
            try:
                os.makedirs(subfolder_path)  # 创建子文件夹
                print(f"创建文件夹: '{subfolder_path}'")
            except Exception as e:
                print(f"创建文件夹 '{subfolder_path}' 失败: {e}")
                continue

        source_path = os.path.join(folder_path, clean_file_name)
        destination_path = os.path.join(subfolder_path, clean_file_name)

        try:
            shutil.move(source_path, destination_path)  # 移动文件
            print(f"移动文件: '{source_path}' 到 '{destination_path}'")
        except Exception as e:
            print(f"移动文件 '{source_path}' 到 '{destination_path}' 失败: {e}")

    # 创建“其他”文件夹
    other_folder_path = os.path.join(folder_path, "其他")
    if not os.path.exists(other_folder_path):
        try:
            os.makedirs(other_folder_path)  # 创建“其他”文件夹
            print(f"创建文件夹: '{other_folder_path}'")
        except Exception as e:
            print(f"创建文件夹 '{other_folder_path}' 失败: {e}")

    # 移动子文件夹中少于2个文件的文件到“其他”文件夹
    for subfolder in os.listdir(folder_path):
        subfolder_path = os.path.join(folder_path, subfolder)
        if os.path.isdir(subfolder_path) and subfolder != "其他":
            subfolder_files = [f for f in os.listdir(subfolder_path) if os.path.isfile(os.path.join(subfolder_path, f))]
            if len(subfolder_files) < 2:
                for file in subfolder_files:
                    source_path = os.path.join(subfolder_path, file)
                    destination_path = os.path.join(other_folder_path, file)
                    try:
                        shutil.move(source_path, destination_path)  # 移动文件到“其他”文件夹
                        print(f"移动文件: '{source_path}' 到 '{destination_path}'")
                    except Exception as e:
                        print(f"移动文件 '{source_path}' 到 '{destination_path}' 失败: {e}")
                try:
                    os.rmdir(subfolder_path)  # 删除空子文件夹
                    print(f"删除文件夹: '{subfolder_path}'")
                except OSError as e:
                    print(f"删除文件夹 '{subfolder_path}' 失败: {e}")


if __name__ == "__main__":
    root = Tk()
    root.withdraw()

    folder_path = askdirectory(title="选择文件夹")

    if folder_path:
        organize_files_by_prefix(folder_path)
        print(f"{folder_path}中的文件已按照前缀整理到子文件夹中")
    else:
        print("未选择文件夹")

    root.quit()
