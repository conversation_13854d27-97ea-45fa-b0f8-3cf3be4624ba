@echo off
chcp 65001 >nul
title Install Right-Click Menu

echo Installing right-click menu...

REM Add registry entries using reg add command
reg add "HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces" /ve /d "Replace Spaces with Underscores" /f >nul
reg add "HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces" /v "Icon" /d "shell32.dll,134" /f >nul
reg add "HKEY_CLASSES_ROOT\*\shell\ReplaceSpaces\command" /ve /d "\"%~dp0replace_spaces_menu.bat\" \"%%1\"" /f >nul

if %errorlevel% equ 0 (
    echo.
    echo Installation successful!
    echo You can now right-click on files and select "Replace Spaces with Underscores"
) else (
    echo.
    echo Installation failed! Please run this script as administrator.
)

echo.
pause
