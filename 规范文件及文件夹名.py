import os
import csv
import json
import tkinter as tk
from tkinter import filedialog, messagebox
from datetime import datetime
import re


def log_operation(log_file, operation_type, old_path, new_path):
    """
    记录操作日志，使用JSON格式以便还原
    operation_type: 'rename' 或 'delete'
    """
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'operation': operation_type,
        'old_path': old_path,
        'new_path': new_path
    }
    
    with open(log_file, 'a', encoding='utf-8') as log:
        json.dump(log_entry, log, ensure_ascii=False)
        log.write('\n')


def record_replacement(replacement_csv, old_str, new_str):
    """记录字符串替换操作"""
    with open(replacement_csv, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow([old_str, new_str])


def record_deletion(deletion_csv, string_to_remove):
    """记录字符串删除操作"""
    with open(deletion_csv, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow([string_to_remove])


def handle_duplicate_name(path, name, extension=''):
    """
    处理重名文件，通过添加数字后缀来避免冲突
    返回不冲突的新文件名
    """
    # 检查是否已有数字后缀
    suffix_pattern = r'(-\d+)$'
    suffix_match = re.search(suffix_pattern, name)
    
    if suffix_match:
        # 如果已有数字后缀，从该数字开始尝试
        base_name = name[:suffix_match.start()]
        current_number = int(suffix_match.group(1)[1:])
        counter = current_number
    else:
        base_name = name
        counter = 1

    full_name = name + extension
    full_path = os.path.join(path, full_name)
    
    # 如果文件名不存在，直接返回原名
    if not os.path.exists(full_path):
        return full_name
    
    # 如果存在重名，则添加或增加数字后缀
    while True:
        new_name = f"{base_name}-{counter}{extension}"
        if not os.path.exists(os.path.join(path, new_name)):
            return new_name
        counter += 1


def normalize_length(name, root_path, min_length=25, max_length=42):
    """
    规范化名称长度，保持在指定范围内
    如果超出最大长度，优先在中文标点处截断，不保留标点
    处理重名情况，同时处理已有的数字后缀
    """
    # 分离文件名和扩展名，改进扩展名判断逻辑
    base_name = name
    extension = ''
    if '.' in name and not name.startswith('.'):
        # 找到最后一个点
        last_dot_index = name.rfind('.')
        potential_ext = name[last_dot_index:]
        
        # 如果扩展名长度合理（不超过5个字符），则认为是真正的扩展名
        if len(potential_ext) <= 6:  # 包括点号在内不超过6个字符
            base_name = name[:last_dot_index]
            extension = potential_ext
        # 否则认为点号是文件名的一部分
    
    # 检查是否已有数字后缀（形如 "-数字" 的格式）
    suffix_pattern = r'(-\d+)(?=[^-\d]|$)'
    suffix_match = None
    if base_name:
        # 找到所有的数字后缀
        all_suffixes = list(re.finditer(suffix_pattern, base_name))
        # 如果有数字后缀，取最后一个
        if all_suffixes:
            suffix_match = all_suffixes[-1]
    
    # 提取基本名称和数字后缀
    number_suffix = ''
    if suffix_match:
        number_suffix = suffix_match.group(1)
        # 保留数字后缀后面的内容
        base_name = base_name[:suffix_match.start()] + base_name[suffix_match.end():]

    # 检查总长度是否在范围内
    if min_length <= len(base_name) <= max_length:
        return name

    # 如果超出最大长度，需要截断
    if len(base_name) > max_length:
        # 中文标点符号列表
        chinese_punctuation = '，。！？；：、''""（）【】《》〈〉「」『』〔〕…—～'
        
        # 在最大长度范围内查找最后一个中文标点
        truncate_pos = -1
        for i in range(max_length - 1, -1, -1):
            if i < len(base_name) and base_name[i] in chinese_punctuation:
                truncate_pos = i
                break
        
        # 如果找到合适的截断点，使用它（不保留标点）；否则强制截断
        if truncate_pos != -1:
            base_name = base_name[:truncate_pos]
        else:
            base_name = base_name[:max_length]

        # 处理重名情况
        return handle_duplicate_name(root_path, base_name, extension)

    # 如果长度小于最小长度，保持原样
    return name


def process_special_extensions(file_path, interactive=False):
    """处理特殊的文件扩展名和大文件"""
    filename, ext = os.path.splitext(file_path)
    new_path = file_path
    changed = False

    # 检查文件是否存在
    if not os.path.exists(file_path):
        return new_path, changed

    try:
        file_size = os.path.getsize(file_path)
        file_size_mb = file_size / (1024 * 1024)

        # 如果文件没有扩展名且大于100MB，添加.7z扩展名
        if not ext and file_size > 100 * 1024 * 1024:
            new_path = filename + '.7z'
            changed = True

        # 如果扩展名中有汉字，移除汉字
        elif any('\u4e00' <= char <= '\u9fff' for char in ext):
            new_ext = ''.join(char for char in ext if not ('\u4e00' <= char <= '\u9fff'))
            new_path = filename + new_ext
            changed = True

        # 将大于300MB的PDF、DOC、TXT文件重命名为.zip
        elif ext.lower() in ['.pdf', '.doc', '.docx', '.txt', '.gif'] and file_size > 300 * 1024 * 1024:
            new_path = filename + '.zip'
            changed = True

        # 将扩展名为.7、.7zz或.z的文件更改为.7z
        elif ext.lower() in ['.7', '.7zz', '.z']:
            new_path = filename + '.7z'
            changed = True

        # 处理大于500MB的未知扩展名或不在处理范围内的扩展名
        elif file_size > 500 * 1024 * 1024 and interactive:
            # 定义已知的扩展名列表
            known_extensions = {
                # 压缩文件
                '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.z',
                # 视频文件
                '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v',
                # 音频文件
                '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
                # 图片文件
                '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.svg',
                # 文档文件
                '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
                # 程序文件
                '.exe', '.msi', '.deb', '.rpm', '.dmg', '.pkg', '.app',
                # 其他常见文件
                '.iso', '.img', '.bin', '.dat', '.log', '.cfg', '.ini'
            }

            # 检查是否为分卷压缩文件（如 .001, .002, .003 等）
            is_volume_file = False
            if ext and re.match(r'^\.\d{3}$', ext):  # 匹配 .001, .002 等格式
                # 检查文件名是否以常见压缩格式结尾
                base_filename = os.path.basename(filename).lower()
                if any(base_filename.endswith(archive_ext) for archive_ext in ['.7z', '.zip', '.rar', '.tar']):
                    is_volume_file = True

            # 检查是否为未知扩展名或空扩展名（排除分卷压缩文件）
            if not is_volume_file and (not ext or ext.lower() not in known_extensions):
                file_display_name = os.path.basename(file_path)
                size_display = f"{file_size_mb:.1f}MB"

                # 弹出确认对话框
                message = f"""发现大文件需要处理扩展名：

文件名：{file_display_name}
文件大小：{size_display}
当前扩展名：{ext if ext else '(无扩展名)'}

此文件大于500MB且扩展名未知或不在常见处理范围内。
建议将扩展名更改为 .7z 以便后续解压缩处理。

是否要将扩展名更改为 .7z？"""

                if messagebox.askyesno("大文件扩展名处理", message):
                    new_path = filename + '.7z'
                    changed = True
                    print(f"用户确认：{file_display_name} -> {os.path.basename(new_path)}")
                else:
                    print(f"用户跳过：{file_display_name}")

    except (OSError, IOError) as e:
        print(f"获取文件大小时出错: {file_path}. 错误: {str(e)}")
        return new_path, changed

    return new_path, changed


def process_files_and_folders(folder_path, replacements_csv, deletions_csv, operations_log):
    """处理文件和文件夹名称"""
    # 读取替换规则
    with open(replacements_csv, newline='', encoding='utf-8') as csvfile:
        replacements = list(csv.reader(csvfile))

    # 读取删除规则
    with open(deletions_csv, newline='', encoding='utf-8') as csvfile:
        deletions = list(csv.reader(csvfile))

    renamed_count = 0
    renamed_dirs = {}  # 用于记录重命名的文件夹及其新路径

    # 首先处理文件夹
    for root, dirs, files in os.walk(folder_path, topdown=False):
        for dir_name in dirs:
            new_dir_name = dir_name

            # 应用删除规则
            for string_to_remove in deletions:
                if string_to_remove[0] in new_dir_name:
                    log_operation(operations_log, 'delete', string_to_remove[0], '')
                    new_dir_name = new_dir_name.replace(string_to_remove[0], "")

            # 应用替换规则
            for old_str, new_str in replacements:
                if old_str in new_dir_name:
                    new_dir_name = new_dir_name.replace(old_str, new_str)

            # 规范化文件夹名长度
            new_dir_name = normalize_length(new_dir_name, root)

            # 重命名文件夹
            if new_dir_name != dir_name:
                old_dir_path = os.path.join(root, dir_name)
                new_dir_path = os.path.join(root, new_dir_name)
                try:
                    os.rename(old_dir_path, new_dir_path)
                    log_operation(operations_log, 'rename', old_dir_path, new_dir_path)
                    renamed_count += 1
                    # 记录文件夹的新旧路径
                    renamed_dirs[old_dir_path] = new_dir_path
                except Exception as e:
                    print(f"重命名文件夹时出错: {old_dir_path} -> {new_dir_path}. 错误: {str(e)}")

    # 然后处理文件
    for root, dirs, files in os.walk(folder_path, topdown=False):
        # 检查当前目录是否是重命名后的目录
        current_root = root
        for old_path, new_path in renamed_dirs.items():
            if root.startswith(old_path):
                current_root = root.replace(old_path, new_path)
                break

        for filename in files:
            try:
                new_filename = filename
                file_path = os.path.join(root, filename)

                # 检查文件是否存在且可访问
                if not os.path.exists(file_path):
                    print(f"文件不存在或无法访问: {file_path}")
                    continue

                # 应用替换规则
                for old_str, new_str in replacements:
                    if old_str in new_filename:
                        new_filename = new_filename.replace(old_str, new_str)

                # 应用删除规则
                for string_to_remove in deletions:
                    if string_to_remove[0] in new_filename:
                        log_operation(operations_log, 'delete', string_to_remove[0], '')
                        new_filename = new_filename.replace(string_to_remove[0], "")

                # 规范化文件名长度
                new_filename = normalize_length(new_filename, current_root)  # 使用更新后的路径

                # 注意：扩展名处理已分离到独立功能，此处不再处理扩展名

                # 如果文件名发生变化，执行重命名
                if new_filename != filename:
                    old_file_path = file_path
                    new_file_path = os.path.join(current_root, new_filename)

                    try:
                        os.rename(old_file_path, new_file_path)
                        log_operation(operations_log, 'rename', old_file_path, new_file_path)
                        renamed_count += 1
                        print(f"重命名文件: {old_file_path} -> {new_file_path}")
                    except Exception as e:
                        print(f"重命名文件时出错: {old_file_path} -> {new_file_path}. 错误: {str(e)}")

            except Exception as e:
                print(f"处理文件时出错: {filename}. 错误: {str(e)}")
                continue

    return renamed_count


def restore_from_log(log_file):
    """根据日志文件还原文件名"""
    if not os.path.exists(log_file):
        messagebox.showerror("错误", "找不到日志文件")
        return 0

    restored_count = 0
    failed_operations = []
    
    # 读取所有日志条目
    with open(log_file, 'r', encoding='utf-8') as log:
        # 将日志条目反序，以便从最新的开始还原
        log_entries = [json.loads(line) for line in log if line.strip()]
        log_entries.reverse()

    # 创建一个集合来跟踪已经还原的路径
    processed_paths = set()

    for entry in log_entries:
        # 如果新路径已经被处理过，跳过
        if entry['new_path'] in processed_paths:
            continue

        try:
            if entry['operation'] == 'rename':
                old_path = entry['old_path']
                new_path = entry['new_path']
                
                # 检查当前文件是否存在
                if os.path.exists(new_path) and not os.path.exists(old_path):
                    # 执行还原
                    os.rename(new_path, old_path)
                    restored_count += 1
                    processed_paths.add(new_path)
                    print(f"已还原: {new_path} -> {old_path}")

        except Exception as e:
            failed_operations.append({
                'path': entry['new_path'],
                'error': str(e)
            })

    # 显示还原结果
    if restored_count > 0:
        messagebox.showinfo("还原完成", f"成功还原了 {restored_count} 个文件/文件夹的名称")
    
    if failed_operations:
        error_message = "以下文件还原失败：\n" + "\n".join(
            f"{op['path']}: {op['error']}" for op in failed_operations
        )
        messagebox.showwarning("还原警告", error_message)

    return restored_count


def show_rules(replacements_csv, deletions_csv):
    """显示当前使用的规则"""
    rules_info = "当前使用的规则：\n\n"
    
    # 读取并显示替换规则
    try:
        with open(replacements_csv, newline='', encoding='utf-8') as csvfile:
            replacements = list(csv.reader(csvfile))
            if replacements:
                rules_info += "替换规则：\n"
                for old_str, new_str in replacements:
                    rules_info += f"  - 将 '{old_str}' 替换为 '{new_str}'\n"
            else:
                rules_info += "替换规则：无\n"
    except Exception as e:
        rules_info += f"读取替换规则出错：{str(e)}\n"

    # 读取并显示删除规则
    try:
        with open(deletions_csv, newline='', encoding='utf-8') as csvfile:
            deletions = list(csv.reader(csvfile))
            if deletions:
                rules_info += "\n删除规则：\n"
                for string_to_remove in deletions:
                    if string_to_remove:  # 确保不是空行
                        rules_info += f"  - 删除 '{string_to_remove[0]}'\n"
            else:
                rules_info += "\n删除规则：无"
    except Exception as e:
        rules_info += f"\n读取删除规则出错：{str(e)}"

    return rules_info


def process_button_click(window):
    """处理规范化按钮点击事件"""
    # 显示功能说明
    function_info = """规范化文件名功能说明：

此功能将应用以下处理规则：
✓ 替换规则（replacements.csv）
✓ 删除规则（deletions.csv）
✓ 文件名长度规范化（25-42字符）
✓ 重名处理（添加数字后缀）
✓ 文件夹名称处理

⚠️ 注意：此功能不再处理扩展名！
如需处理扩展名，请使用"解压缩预处理扩展名"功能。

是否继续？"""

    if not messagebox.askyesno("确认规范化", function_info):
        return

    # 设置默认路径
    default_path = r"D:\BaiduYunDownload"
    folder_path = filedialog.askdirectory(
        parent=window,
        initialdir=default_path,
        title="选择要处理的文件夹"
    )
    
    if not folder_path:
        return

    # 定义配置文件路径
    config_dir = r"D:\SynologyDrive\整理\file_renamer_config"
    os.makedirs(config_dir, exist_ok=True)

    replacements_csv = os.path.join(config_dir, "replacements.csv")
    deletions_csv = os.path.join(config_dir, "deletions.csv")
    operations_log = os.path.join(config_dir, "operations.log")

    # 确保配置文件存在并打印路径信息
    print(f"配置文件路径：")
    print(f"替换规则文件：{replacements_csv}")
    print(f"删除规则文件：{deletions_csv}")
    
    for file in [replacements_csv, deletions_csv]:
        if not os.path.exists(file):
            print(f"创建文件：{file}")
            with open(file, 'w', newline='', encoding='utf-8') as f:
                pass
        else:
            print(f"文件已存在：{file}")

    # 显示当前使用的规则
    rules_info = show_rules(replacements_csv, deletions_csv)
    #print(f"规则信息：\n{rules_info}")  # 添加调试信息
    #if not messagebox.askyesno("确认规则", f"{rules_info}\n\n是否继续处理文件？"):
    #    return

    # 处理文件和文件夹
    renamed_count = process_files_and_folders(folder_path, replacements_csv, deletions_csv, operations_log)

    # 显示处理结果
    if renamed_count > 0:
        messagebox.showinfo("处理完成", f"共重命名了 {renamed_count} 个文件/文件夹\n详细信息请查看日志文件：{operations_log}")
    else:
        messagebox.showinfo("处理完成", "没有文件需要重命名。")


def restore_button_click(window):
    """处理还原按钮点击事件"""
    config_dir = r"D:\SynologyDrive\整理\file_renamer_config"
    operations_log = os.path.join(config_dir, "operations.log")

    if not os.path.exists(operations_log):
        messagebox.showerror("错误", "找不到操作日志文件")
        return

    # 询问用户是否确定要还原
    if messagebox.askyesno("确认还原", "是否要还原最近的文件名修改？\n此操作不可撤销！"):
        restore_from_log(operations_log)


def process_extensions_button_click(window):
    """处理解压缩预处理扩展名按钮点击事件"""
    # 设置默认路径
    default_path = r"D:\BaiduYunDownload"
    folder_path = filedialog.askdirectory(
        parent=window,
        initialdir=default_path,
        title="选择要处理扩展名的文件夹"
    )

    if not folder_path:
        return

    # 定义配置文件路径
    config_dir = r"D:\SynologyDrive\整理\file_renamer_config"
    os.makedirs(config_dir, exist_ok=True)
    operations_log = os.path.join(config_dir, "operations.log")

    # 显示扩展名处理规则
    rules_info = """即将应用的扩展名处理规则：

⚠️ 注意：此功能仅处理文件扩展名，不会应用重命名规则！

1. 无扩展名且文件大小 > 100MB：添加 .7z 扩展名
2. 扩展名包含汉字：移除汉字字符
3. 大文件重命名规则（文件大小 > 300MB）：
   - .pdf → .zip
   - .doc/.docx → .zip
   - .txt → .zip
   - .gif → .zip
4. 扩展名标准化：
   - .7 → .7z
   - .7zz → .7z
   - .z → .7z
5. 🆕 分卷压缩文件智能识别：
   - 自动识别 7z.001, 7z.002, zip.003 等分卷文件
   - 不会误认为未知扩展名
6. 🆕 大文件智能处理（文件大小 > 500MB）：
   - 检测真正的未知扩展名（排除分卷压缩文件）
   - 弹出确认对话框，提供自动更改为 .7z 的选项
   - 用户可选择确认或跳过

此功能独立于"规范化文件名"功能，不会：
• 应用替换规则（replacements.csv）
• 应用删除规则（deletions.csv）
• 修改文件名长度
• 处理文件夹名称

是否继续处理？"""

    if not messagebox.askyesno("确认处理扩展名", rules_info):
        return

    # 处理扩展名
    processed_count = process_extensions_only(folder_path, operations_log)

    # 显示处理结果
    if processed_count > 0:
        messagebox.showinfo("处理完成", f"共处理了 {processed_count} 个文件的扩展名\n详细信息请查看日志文件：{operations_log}")
    else:
        messagebox.showinfo("处理完成", "没有文件需要处理扩展名。")


def process_extensions_only(folder_path, operations_log):
    """仅处理文件扩展名，不使用重命名规则"""
    processed_count = 0

    for root, _, files in os.walk(folder_path):
        for filename in files:
            try:
                file_path = os.path.join(root, filename)

                # 检查文件是否存在且可访问
                if not os.path.exists(file_path):
                    print(f"文件不存在或无法访问: {file_path}")
                    continue

                # 仅处理扩展名相关的逻辑，不使用重命名规则，启用交互模式
                new_file_path, ext_changed = process_special_extensions(file_path, interactive=True)

                if ext_changed:
                    try:
                        os.rename(file_path, new_file_path)
                        log_operation(operations_log, 'rename', file_path, new_file_path)
                        processed_count += 1
                        print(f"处理扩展名: {os.path.basename(file_path)} -> {os.path.basename(new_file_path)}")
                    except Exception as e:
                        print(f"处理扩展名时出错: {file_path} -> {new_file_path}. 错误: {str(e)}")

            except Exception as e:
                print(f"处理文件时出错: {filename}. 错误: {str(e)}")
                continue

    return processed_count


def show_extension_rules(window):
    """显示扩展名处理规则"""
    rules_info = """扩展名处理规则详细说明：

⚠️ 重要说明：此功能仅处理文件扩展名，完全独立于重命名规则！

=== 自动添加扩展名规则 ===
• 无扩展名且文件大小 > 100MB
  → 自动添加 .7z 扩展名

=== 扩展名清理规则 ===
• 扩展名包含汉字字符
  → 移除所有汉字，保留其他字符
  示例：.txt汉字 → .txt

=== 大文件重命名规则（文件大小 > 300MB）===
• .pdf 文件 → 重命名为 .zip
• .doc 文件 → 重命名为 .zip
• .docx 文件 → 重命名为 .zip
• .txt 文件 → 重命名为 .zip
• .gif 文件 → 重命名为 .zip

=== 扩展名标准化规则 ===
• .7 → 标准化为 .7z
• .7zz → 标准化为 .7z
• .z → 标准化为 .7z

=== 分卷压缩文件识别 ===
• 自动识别分卷压缩文件（如 7z.001, 7z.002, zip.003 等）
• 支持格式：.7z.001, .zip.001, .rar.001, .tar.001 等
• 这些文件不会被误认为未知扩展名

=== 大文件智能处理规则（文件大小 > 500MB）===
• 检测未知扩展名或不在常见处理范围内的扩展名
• 已知扩展名包括：
  - 压缩文件：.zip, .rar, .7z, .tar, .gz 等
  - 视频文件：.mp4, .avi, .mkv, .mov, .wmv 等
  - 音频文件：.mp3, .wav, .flac, .aac, .ogg 等
  - 图片文件：.jpg, .png, .gif, .bmp, .tiff 等
  - 文档文件：.pdf, .doc, .docx, .xls, .ppt 等
  - 程序文件：.exe, .msi, .deb, .rpm, .dmg 等
• 自动排除分卷压缩文件（不会弹出处理对话框）
• 对于真正未知扩展名的大文件，弹出确认对话框
• 用户可选择将扩展名更改为 .7z 或跳过处理

=== 处理逻辑 ===
1. 遍历指定文件夹及所有子文件夹
2. 仅检查文件的扩展名和大小
3. 按照上述规则进行处理
4. 记录所有操作到日志文件
5. 支持通过"还原文件名"功能撤销操作

=== 与"规范化文件名"功能的区别 ===
扩展名处理功能 不会：
• 应用替换规则（replacements.csv）
• 应用删除规则（deletions.csv）
• 修改文件名长度或截断文件名
• 处理文件夹名称
• 应用重名处理逻辑

=== 注意事项 ===
• 处理前会显示确认对话框
• 所有操作都会记录日志
• 可通过还原功能撤销修改
• 建议先备份重要文件
• 此功能专门用于解压缩前的扩展名预处理"""

    # 创建显示窗口
    rules_window = tk.Toplevel(window)
    rules_window.title("扩展名处理规则")
    rules_window.geometry("650x600")
    rules_window.configure(bg='#f0f0f0')

    # 创建文本框和滚动条
    text_frame = tk.Frame(rules_window, bg='#f0f0f0')
    text_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

    # 创建滚动条
    scrollbar = tk.Scrollbar(text_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # 创建文本框
    text_widget = tk.Text(
        text_frame,
        wrap=tk.WORD,
        yscrollcommand=scrollbar.set,
        font=('Microsoft YaHei', 10),
        bg='white',
        fg='black',
        padx=10,
        pady=10
    )
    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

    # 配置滚动条
    scrollbar.config(command=text_widget.yview)

    # 插入规则信息
    text_widget.insert(tk.END, rules_info)
    text_widget.config(state=tk.DISABLED)  # 设为只读

    # 添加关闭按钮
    close_button = tk.Button(
        rules_window,
        text="关闭",
        command=rules_window.destroy,
        width=12,
        height=2,
        bg='#607D8B',
        fg='white',
        font=('Microsoft YaHei', 10)
    )
    close_button.pack(pady=15)


def edit_rules_button_click(window):
    """处理编辑规则按钮点击事件"""
    config_dir = r"D:\SynologyDrive\整理\file_renamer_config"
    replacements_csv = os.path.join(config_dir, "replacements.csv")
    deletions_csv = os.path.join(config_dir, "deletions.csv")

    # 确保配置文件存在
    os.makedirs(config_dir, exist_ok=True)
    for file_path in [replacements_csv, deletions_csv]:
        if not os.path.exists(file_path):
            with open(file_path, 'w', newline='', encoding='utf-8'):
                pass

    # 创建编辑窗口
    edit_window = tk.Toplevel(window)
    edit_window.title("编辑重命名规则")
    edit_window.geometry("800x600")
    edit_window.configure(bg='#f0f0f0')

    # 创建主框架
    main_frame = tk.Frame(edit_window, bg='#f0f0f0')
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # 创建左右分栏
    left_frame = tk.Frame(main_frame, bg='#f0f0f0')
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

    right_frame = tk.Frame(main_frame, bg='#f0f0f0')
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

    # 左侧：替换规则
    tk.Label(left_frame, text="替换规则 (格式: 原文本,新文本)",
             font=('Microsoft YaHei', 12, 'bold'), bg='#f0f0f0').pack(pady=(0, 5))

    # 替换规则文本框
    replace_text_frame = tk.Frame(left_frame, bg='#f0f0f0')
    replace_text_frame.pack(fill=tk.BOTH, expand=True)

    replace_scrollbar = tk.Scrollbar(replace_text_frame)
    replace_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    replace_text = tk.Text(
        replace_text_frame,
        wrap=tk.NONE,
        yscrollcommand=replace_scrollbar.set,
        font=('Consolas', 10),
        bg='white',
        fg='black'
    )
    replace_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    replace_scrollbar.config(command=replace_text.yview)

    # 右侧：删除规则
    tk.Label(right_frame, text="删除规则 (每行一个要删除的文本)",
             font=('Microsoft YaHei', 12, 'bold'), bg='#f0f0f0').pack(pady=(0, 5))

    # 删除规则文本框
    delete_text_frame = tk.Frame(right_frame, bg='#f0f0f0')
    delete_text_frame.pack(fill=tk.BOTH, expand=True)

    delete_scrollbar = tk.Scrollbar(delete_text_frame)
    delete_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    delete_text = tk.Text(
        delete_text_frame,
        wrap=tk.NONE,
        yscrollcommand=delete_scrollbar.set,
        font=('Consolas', 10),
        bg='white',
        fg='black'
    )
    delete_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    delete_scrollbar.config(command=delete_text.yview)

    # 加载现有规则
    def load_rules():
        # 加载替换规则
        try:
            with open(replacements_csv, 'r', encoding='utf-8') as f:
                replace_text.delete(1.0, tk.END)
                replace_text.insert(1.0, f.read())
        except Exception as e:
            print(f"加载替换规则出错: {e}")

        # 加载删除规则
        try:
            with open(deletions_csv, 'r', encoding='utf-8') as f:
                delete_text.delete(1.0, tk.END)
                delete_text.insert(1.0, f.read())
        except Exception as e:
            print(f"加载删除规则出错: {e}")

    # 保存规则
    def save_rules():
        try:
            # 保存替换规则
            with open(replacements_csv, 'w', encoding='utf-8') as f:
                f.write(replace_text.get(1.0, tk.END))

            # 保存删除规则
            with open(deletions_csv, 'w', encoding='utf-8') as f:
                f.write(delete_text.get(1.0, tk.END))

            messagebox.showinfo("保存成功", "规则已保存成功！")
        except Exception as e:
            messagebox.showerror("保存失败", f"保存规则时出错：{str(e)}")

    # 按钮框架
    button_frame = tk.Frame(edit_window, bg='#f0f0f0')
    button_frame.pack(fill=tk.X, padx=10, pady=10)

    # 保存按钮
    save_button = tk.Button(
        button_frame,
        text="保存规则",
        command=save_rules,
        width=12,
        height=2,
        bg='#4CAF50',
        fg='white',
        font=('Microsoft YaHei', 10, 'bold')
    )
    save_button.pack(side=tk.LEFT, padx=(0, 10))

    # 重新加载按钮
    reload_button = tk.Button(
        button_frame,
        text="重新加载",
        command=load_rules,
        width=12,
        height=2,
        bg='#2196F3',
        fg='white',
        font=('Microsoft YaHei', 10)
    )
    reload_button.pack(side=tk.LEFT, padx=(0, 10))

    # 关闭按钮
    close_button = tk.Button(
        button_frame,
        text="关闭",
        command=edit_window.destroy,
        width=12,
        height=2,
        bg='#f44336',
        fg='white',
        font=('Microsoft YaHei', 10)
    )
    close_button.pack(side=tk.RIGHT)

    # 初始加载规则
    load_rules()


def show_rules_button_click(window):
    """处理显示规则按钮点击事件"""
    config_dir = r"D:\SynologyDrive\整理\file_renamer_config"
    replacements_csv = os.path.join(config_dir, "replacements.csv")
    deletions_csv = os.path.join(config_dir, "deletions.csv")

    # 确保配置文件存在
    for file_path in [replacements_csv, deletions_csv]:
        if not os.path.exists(file_path):
            with open(file_path, 'w', newline='', encoding='utf-8'):
                pass

    # 获取规则信息
    rules_info = show_rules(replacements_csv, deletions_csv)

    # 添加重命名规则说明
    rules_info += "\n\n重命名规则说明：\n"
    rules_info += "1. 长度规范：文件名长度保持在25-42个字符之间\n"
    rules_info += "2. 截断规则：超长文件名优先在中文标点处截断，不保留标点\n"
    rules_info += "3. 重名处理：重名文件自动添加数字后缀（-1, -2, ...）\n"
    rules_info += "4. 文件夹处理：同样应用替换和删除规则\n"
    rules_info += "\n⚠️ 重要说明：\n"
    rules_info += "扩展名处理已分离为独立功能！\n"
    rules_info += "如需处理扩展名，请使用'解压缩预处理扩展名'功能。\n"

    # 创建新窗口显示规则
    rules_window = tk.Toplevel(window)
    rules_window.title("重命名规则")
    rules_window.geometry("600x500")
    rules_window.configure(bg='#f0f0f0')

    # 创建文本框和滚动条
    text_frame = tk.Frame(rules_window, bg='#f0f0f0')
    text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # 创建滚动条
    scrollbar = tk.Scrollbar(text_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # 创建文本框
    text_widget = tk.Text(
        text_frame,
        wrap=tk.WORD,
        yscrollcommand=scrollbar.set,
        font=('Microsoft YaHei', 10),
        bg='white',
        fg='black'
    )
    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

    # 配置滚动条
    scrollbar.config(command=text_widget.yview)

    # 插入规则信息
    text_widget.insert(tk.END, rules_info)
    text_widget.config(state=tk.DISABLED)  # 设为只读

    # 添加关闭按钮
    close_button = tk.Button(
        rules_window,
        text="关闭",
        command=rules_window.destroy,
        width=10,
        height=1
    )
    close_button.pack(pady=10)


def create_gui():
    """创建图形用户界面"""
    window = tk.Tk()
    window.title("文件名规范化工具")
    window.geometry("500x480")

    # 设置窗口样式
    window.configure(bg='#f0f0f0')

    # 创建主框架
    main_frame = tk.Frame(window, bg='#f0f0f0')
    main_frame.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)

    # 重命名功能区域
    rename_frame = tk.LabelFrame(
        main_frame,
        text="文件名重命名",
        font=('Microsoft YaHei', 12, 'bold'),
        bg='#f0f0f0',
        fg='#333333',
        padx=10,
        pady=10
    )
    rename_frame.pack(fill=tk.X, pady=(0, 15))

    # 主要重命名按钮
    normalize_button = tk.Button(
        rename_frame,
        text="规范化文件名",
        command=lambda: process_button_click(window),
        width=25,
        height=2,
        bg='#4CAF50',
        fg='white',
        font=('Microsoft YaHei', 11, 'bold')
    )
    normalize_button.pack(pady=(0, 10))

    # 规则管理子按钮框架
    rules_sub_frame = tk.Frame(rename_frame, bg='#f0f0f0')
    rules_sub_frame.pack(fill=tk.X)

    # 编辑规则按钮（左侧）
    edit_rules_button = tk.Button(
        rules_sub_frame,
        text="编辑规则",
        command=lambda: edit_rules_button_click(window),
        width=12,
        height=1,
        bg='#9C27B0',
        fg='white',
        font=('Microsoft YaHei', 9)
    )
    edit_rules_button.pack(side=tk.LEFT, padx=(0, 5))

    # 显示规则按钮（右侧）
    show_rules_button = tk.Button(
        rules_sub_frame,
        text="显示规则",
        command=lambda: show_rules_button_click(window),
        width=12,
        height=1,
        bg='#2196F3',
        fg='white',
        font=('Microsoft YaHei', 9)
    )
    show_rules_button.pack(side=tk.RIGHT, padx=(5, 0))

    # 扩展名处理功能区域
    extension_frame = tk.LabelFrame(
        main_frame,
        text="扩展名处理",
        font=('Microsoft YaHei', 12, 'bold'),
        bg='#f0f0f0',
        fg='#333333',
        padx=10,
        pady=10
    )
    extension_frame.pack(fill=tk.X, pady=(0, 15))

    # 解压缩预处理扩展名按钮
    process_extensions_button = tk.Button(
        extension_frame,
        text="解压缩预处理扩展名",
        command=lambda: process_extensions_button_click(window),
        width=25,
        height=2,
        bg='#607D8B',
        fg='white',
        font=('Microsoft YaHei', 10, 'bold')
    )
    process_extensions_button.pack(pady=(0, 10))

    # 扩展名规则管理子按钮框架
    ext_rules_sub_frame = tk.Frame(extension_frame, bg='#f0f0f0')
    ext_rules_sub_frame.pack(fill=tk.X)

    # 显示扩展名规则按钮
    show_ext_rules_button = tk.Button(
        ext_rules_sub_frame,
        text="显示扩展名规则",
        command=lambda: show_extension_rules(window),
        width=15,
        height=1,
        bg='#546E7A',
        fg='white',
        font=('Microsoft YaHei', 9)
    )
    show_ext_rules_button.pack(pady=5)

    # 其他功能区域
    other_frame = tk.LabelFrame(
        main_frame,
        text="其他功能",
        font=('Microsoft YaHei', 12, 'bold'),
        bg='#f0f0f0',
        fg='#333333',
        padx=10,
        pady=10
    )
    other_frame.pack(fill=tk.X, pady=(0, 15))

    # 还原按钮
    restore_button = tk.Button(
        other_frame,
        text="还原文件名",
        command=lambda: restore_button_click(window),
        width=25,
        height=2,
        bg='#FF9800',
        fg='white',
        font=('Microsoft YaHei', 10)
    )
    restore_button.pack(pady=5)

    # 底部按钮框架
    bottom_frame = tk.Frame(main_frame, bg='#f0f0f0')
    bottom_frame.pack(fill=tk.X, pady=(10, 0))

    # 退出按钮
    exit_button = tk.Button(
        bottom_frame,
        text="退出",
        command=window.quit,
        width=15,
        height=2,
        bg='#f44336',
        fg='white',
        font=('Microsoft YaHei', 10)
    )
    exit_button.pack()

    return window


def main():
    window = create_gui()
    window.mainloop()


if __name__ == "__main__":
    main()
