# -*- coding: utf-8 -*-
import os
import time
import subprocess
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import datetime
import json
import threading
import re
from queue import Queue


class UnzipGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("智能解压缩工具")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 设置窗口居中
        self.center_window()
        
        # 创建队列用于线程间通信
        self.message_queue = Queue()
        
        # 状态变量
        self.is_processing = False
        self.is_normalizing = False
        self.current_process = None
        self.newly_extracted_dirs = set()

        # 撤销功能相关变量
        self.rename_history = []  # 记录重命名操作历史 [(old_path, new_path), ...]
        
        # 创建界面
        self.create_widgets()
        
        # 启动消息处理
        self.process_queue()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 文件夹选择区域
        folder_frame = ttk.LabelFrame(main_frame, text="文件夹选择", padding="5")
        folder_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        folder_frame.columnconfigure(1, weight=1)
        
        ttk.Label(folder_frame, text="目标文件夹:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.folder_var = tk.StringVar(value="D:\\BaiduYunDownload\\")
        self.folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_var, width=60)
        self.folder_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(folder_frame, text="浏览", command=self.browse_folder).grid(row=0, column=2)
        
        # 操作按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, columnspan=2, pady=(0, 10))
        
        self.start_button = ttk.Button(button_frame, text="开始解压", command=self.start_extraction)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止解压", command=self.stop_extraction, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="编辑密码字典", command=self.edit_password_dict).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="规范文件后缀", command=self.normalize_extensions).pack(side=tk.LEFT, padx=(0, 10))

        # 撤销按钮
        self.undo_button = ttk.Button(button_frame, text="撤销重命名", command=self.undo_rename, state="disabled")
        self.undo_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="查看日志", command=self.view_logs).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清空日志", command=self.clear_output).pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度显示区域
        progress_frame = ttk.LabelFrame(main_frame, text="解压进度", padding="5")
        progress_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(progress_frame, textvariable=self.progress_var).grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 统计信息区域
        stats_frame = ttk.LabelFrame(main_frame, text="统计信息", padding="5")
        stats_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        stats_inner = ttk.Frame(stats_frame)
        stats_inner.pack(fill=tk.X)
        
        ttk.Label(stats_inner, text="成功解压:").grid(row=0, column=0, sticky=tk.W)
        self.success_var = tk.StringVar(value="0")
        ttk.Label(stats_inner, textvariable=self.success_var, foreground="green").grid(row=0, column=1, sticky=tk.W, padx=(5, 20))
        
        ttk.Label(stats_inner, text="解压失败:").grid(row=0, column=2, sticky=tk.W)
        self.failed_var = tk.StringVar(value="0")
        self.failed_button = tk.Button(stats_inner, textvariable=self.failed_var,
                                      foreground="red", background="white",
                                      relief="flat", borderwidth=0,
                                      cursor="hand2", command=self.show_failed_files,
                                      font=("TkDefaultFont", 9, "underline"))
        self.failed_button.grid(row=0, column=3, sticky=tk.W, padx=(5, 20))

        # 添加鼠标悬停效果
        def on_enter(event):
            self.failed_button.config(foreground="darkred", background="#f0f0f0")

        def on_leave(event):
            self.failed_button.config(foreground="red", background="white")

        self.failed_button.bind("<Enter>", on_enter)
        self.failed_button.bind("<Leave>", on_leave)
        
        ttk.Label(stats_inner, text="跳过文件:").grid(row=0, column=4, sticky=tk.W)
        self.skipped_var = tk.StringVar(value="0")
        self.skipped_button = tk.Button(stats_inner, textvariable=self.skipped_var,
                                       foreground="orange", background="white",
                                       relief="flat", borderwidth=0,
                                       cursor="hand2", command=self.show_skipped_files,
                                       state="disabled")
        self.skipped_button.grid(row=0, column=5, sticky=tk.W, padx=(5, 0))

        # 为跳过按钮添加悬停效果
        def on_enter_skipped(e):
            if self.skipped_button['state'] == 'normal':
                self.skipped_button.config(background="#fff3cd")

        def on_leave_skipped(e):
            self.skipped_button.config(background="white")

        self.skipped_button.bind("<Enter>", on_enter_skipped)
        self.skipped_button.bind("<Leave>", on_leave_skipped)
        
        # 输出日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 初始化统计
        self.success_count = 0
        self.failed_count = 0
        self.skipped_count = 0

        # 记录失败文件的详细信息
        self.failed_files = []

        # 记录跳过文件的详细信息
        self.skipped_files = []
        
    def browse_folder(self):
        """浏览文件夹"""
        folder = filedialog.askdirectory(initialdir=self.folder_var.get())
        if folder:
            self.folder_var.set(folder)
            
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.datetime.now().strftime('%H:%M:%S')
        if level == "ERROR":
            formatted_message = f"[{timestamp}] ❌ {message}\n"
        elif level == "SUCCESS":
            formatted_message = f"[{timestamp}] ✅ {message}\n"
        elif level == "WARNING":
            formatted_message = f"[{timestamp}] ⚠️ {message}\n"
        else:
            formatted_message = f"[{timestamp}] ℹ️ {message}\n"
            
        self.message_queue.put(('log', formatted_message))
        
    def update_progress(self, message):
        """更新进度显示"""
        self.message_queue.put(('progress', message))
        
    def update_stats(self, stat_type):
        """更新统计信息"""
        self.message_queue.put(('stats', stat_type))
        
    def process_queue(self):
        """处理消息队列"""
        try:
            while True:
                msg_type, data = self.message_queue.get_nowait()
                
                if msg_type == 'log':
                    self.log_text.insert(tk.END, data)
                    self.log_text.see(tk.END)
                elif msg_type == 'progress':
                    self.progress_var.set(data)
                elif msg_type == 'stats':
                    if data == 'success':
                        self.success_count += 1
                        self.success_var.set(str(self.success_count))
                    elif data == 'failed':
                        self.failed_count += 1
                        self.failed_var.set(str(self.failed_count))
                        # 更新失败按钮状态
                        if self.failed_count > 0:
                            self.failed_button.config(cursor="hand2", state="normal")
                        else:
                            self.failed_button.config(cursor="arrow", state="disabled")
                    elif data == 'skipped':
                        self.skipped_count += 1
                        self.skipped_var.set(str(self.skipped_count))
                        # 更新跳过按钮状态
                        if self.skipped_count > 0:
                            self.skipped_button.config(cursor="hand2", state="normal")
                        else:
                            self.skipped_button.config(cursor="arrow", state="disabled")
                elif msg_type == 'finished':
                    self.extraction_finished()
                    
        except:
            pass
            
        # 每100ms检查一次队列
        self.root.after(100, self.process_queue)
        
    def start_extraction(self):
        """开始解压"""
        folder_path = self.folder_var.get().strip()
        if not folder_path:
            messagebox.showerror("错误", "请选择要解压的文件夹")
            return
            
        if not os.path.exists(folder_path):
            messagebox.showerror("错误", "选择的文件夹不存在")
            return
            
        # 重置统计
        self.success_count = 0
        self.failed_count = 0
        self.skipped_count = 0
        self.success_var.set("0")
        self.failed_var.set("0")
        self.skipped_var.set("0")
        self.newly_extracted_dirs.clear()
        self.failed_files.clear()
        self.skipped_files.clear()

        # 重置失败按钮状态
        self.failed_button.config(cursor="arrow", state="disabled")
        
        # 更新界面状态
        self.is_processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar.start()
        
        # 清空日志
        self.log_text.delete(1.0, tk.END)
        
        # 在新线程中执行解压
        self.extraction_thread = threading.Thread(target=self.unzip_files_thread, args=(folder_path,))
        self.extraction_thread.daemon = True
        self.extraction_thread.start()
        
    def stop_extraction(self):
        """停止解压"""
        self.is_processing = False
        self.is_normalizing = False  # 同时停止文件规范化

        # 强制终止当前7z进程
        if self.current_process:
            try:
                self.log_message("正在终止7z进程...")
                # 尝试优雅地终止进程
                self.current_process.terminate()

                # 等待进程结束，最多等待3秒
                try:
                    self.current_process.wait(timeout=3)
                    self.log_message("7z进程已正常终止")
                except subprocess.TimeoutExpired:
                    # 如果3秒后进程仍未结束，强制杀死
                    self.log_message("7z进程未响应，强制终止...")
                    self.current_process.kill()
                    try:
                        self.current_process.wait(timeout=2)
                        self.log_message("7z进程已强制终止")
                    except subprocess.TimeoutExpired:
                        self.log_message("无法终止7z进程，可能需要手动结束", "ERROR")

            except Exception as e:
                self.log_message(f"终止7z进程时出错：{e}", "ERROR")
            finally:
                self.current_process = None

        self.log_message("用户停止了解压操作", "WARNING")
        self.message_queue.put(('finished', None))
        
    def extraction_finished(self):
        """解压完成"""
        self.is_processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_bar.stop()
        self.update_progress("解压完成")
        
        # 显示完成统计
        total = self.success_count + self.failed_count + self.skipped_count
        self.log_message(f"解压任务完成！总计处理 {total} 个文件，成功 {self.success_count} 个，失败 {self.failed_count} 个，跳过 {self.skipped_count} 个", "SUCCESS")

    def unzip_files_thread(self, folder_path):
        """在线程中执行解压操作"""
        try:
            self.log_message(f"开始扫描文件夹: {folder_path}")
            self.update_progress("正在扫描文件...")

            # 获取密码字典文件的路径
            password_dict_path = os.path.join(folder_path, 'passdict.txt')
            has_password_dict = os.path.isfile(password_dict_path)
            if not has_password_dict:
                self.log_message(f"未找到密码字典文件：{password_dict_path}，将跳过密码字典尝试", "WARNING")
            else:
                self.log_message(f"找到密码字典文件：{password_dict_path}")

            # 遍历所有子文件夹和文件
            for root, dirs, files in os.walk(folder_path):
                if not self.is_processing:
                    break

                # 检查当前目录是否是新解压的文件夹的子目录
                is_in_new_dir = False
                for new_dir in self.newly_extracted_dirs:
                    if root.startswith(new_dir + os.sep):
                        self.log_message(f"跳过新解压文件夹中的内容：{root}")
                        dirs.clear()  # 跳过子目录
                        is_in_new_dir = True
                        break

                if is_in_new_dir:
                    continue

                for file in files:
                    if not self.is_processing:
                        break

                    file_path = os.path.join(root, file)

                    # 跳过正在下载的文件
                    if file.endswith('.downloading'):
                        self.log_message(f"跳过正在下载的文件：{file_path}")
                        self.record_skipped_file(file_path, "正在下载的文件")
                        self.update_stats('skipped')
                        continue

                    if file.endswith(('.zip', '.ZIP', '.rar', '.RAR', '.tar', '.TAR', '.7z', '.7Z', '.7z.001', '.zip.001', '.z01', '.z02')):
                        # 检查是否是分卷压缩包的主文件
                        if not self.is_split_archive_main_file(file_path):
                            self.log_message(f"跳过分卷压缩包的非主文件：{file_path}")
                            self.record_skipped_file(file_path, "分卷压缩包的非主文件")
                            self.update_stats('skipped')
                            continue

                        self.log_message(f"正在处理压缩文件：{file_path}")
                        self.update_progress(f"正在解压: {os.path.basename(file_path)}")

                        # 1. 首先尝试查找解压密码.txt等文件
                        extract_passwords = self.get_all_extract_passwords(root, folder_path)
                        if extract_passwords:
                            for extract_password in extract_passwords:
                                if extract_password is None:  # 解压密码文件指示无密码
                                    self.log_message("解压密码文件指示无密码，直接尝试无密码解压...")
                                    if self.extract_without_password(file_path, root):
                                        self.log_message(f"成功解压文件：{file_path}", "SUCCESS")
                                        self.update_stats('success')
                                        # 记录新解压的目录
                                        self.newly_extracted_dirs.add(root)
                                        break
                                elif extract_password:  # 找到了具体密码
                                    self.log_message(f"使用解压密码文件中的密码尝试解压...")
                                    if self.extract_with_7z(file_path, root, extract_password):
                                        self.log_message(f"成功解压文件：{file_path}", "SUCCESS")
                                        self.update_stats('success')
                                        # 记录新解压的目录
                                        self.newly_extracted_dirs.add(root)
                                        break
                            else:
                                # 如果所有解压密码都失败了，继续尝试其他方法
                                pass

                            # 如果解压成功，跳到下一个文件
                            if root in self.newly_extracted_dirs:
                                continue

                        # 2. 然后尝试查找 password.txt
                        password = self.get_password(root, folder_path)
                        if password is not None:  # 找到了密码文件
                            if password == '':  # 空字符串表示没找到有效密码
                                pass  # 继续尝试其他方法
                            else:  # 找到了具体密码
                                self.log_message(f"使用 password.txt 中的密码尝试解压...")
                                if self.extract_with_7z(file_path, root, password):
                                    self.log_message(f"成功解压文件：{file_path}", "SUCCESS")
                                    self.update_stats('success')
                                    # 记录新解压的目录
                                    self.newly_extracted_dirs.add(root)
                                    continue
                        elif password is None:  # 密码文件指示无密码
                            self.log_message("password.txt文件指示无密码，直接尝试无密码解压...")
                            if self.extract_without_password(file_path, root):
                                self.log_message(f"成功解压文件：{file_path}", "SUCCESS")
                                self.update_stats('success')
                                # 记录新解压的目录
                                self.newly_extracted_dirs.add(root)
                                continue

                        # 3. 尝试使用密码字典（如果存在）
                        if has_password_dict:
                            self.log_message("尝试使用密码字典解压...")
                            if self.try_password_dict(file_path, root, password_dict_path):
                                self.log_message(f"成功解压文件：{file_path}", "SUCCESS")
                                self.update_stats('success')
                                # 记录新解压的目录
                                self.newly_extracted_dirs.add(root)
                                continue

                        # 4. 尝试无密码解压
                        self.log_message("尝试无密码解压...")
                        if self.extract_without_password(file_path, root):
                            self.log_message(f"成功解压文件：{file_path}", "SUCCESS")
                            self.update_stats('success')
                            # 记录新解压的目录
                            self.newly_extracted_dirs.add(root)
                            continue

                        # 如果所有方法都失败，输出提示并继续处理下一个文件
                        self.log_message(f"文件 {file_path} 所有解压方法都失败，跳过此文件", "ERROR")
                        self.record_failed_file(file_path, "所有解压方法都失败")
                        self.update_stats('failed')
                        continue

        except Exception as e:
            self.log_message(f"解压过程出现异常：{e}", "ERROR")
        finally:
            self.message_queue.put(('finished', None))

    def record_failed_file(self, file_path, reason):
        """记录失败的文件信息"""
        failed_info = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'directory': os.path.dirname(file_path),
            'reason': reason,
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'file_size': self.get_file_size_str(file_path)
        }
        self.failed_files.append(failed_info)

    def record_skipped_file(self, file_path, reason):
        """记录跳过的文件信息"""
        skipped_info = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'directory': os.path.dirname(file_path),
            'reason': reason,
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'file_size': self.get_file_size_str(file_path)
        }
        self.skipped_files.append(skipped_info)

    def get_file_size_str(self, file_path):
        """获取文件大小的字符串表示"""
        try:
            size = os.path.getsize(file_path)
            return self.get_file_size_str_from_bytes(size)
        except:
            return "未知大小"

    def get_file_size_str_from_bytes(self, size):
        """从字节数获取文件大小的字符串表示"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"

    def show_failed_files(self):
        """显示失败文件的详细信息"""
        if not self.failed_files:
            messagebox.showinfo("信息", "当前没有解压失败的文件")
            return

        # 创建失败文件查看窗口
        failed_window = tk.Toplevel(self.root)
        failed_window.title(f"解压失败文件详情 ({len(self.failed_files)} 个)")
        failed_window.geometry("1000x600")

        # 设置窗口居中
        failed_window.update_idletasks()
        width = failed_window.winfo_width()
        height = failed_window.winfo_height()
        x = (failed_window.winfo_screenwidth() // 2) - (width // 2)
        y = (failed_window.winfo_screenheight() // 2) - (height // 2)
        failed_window.geometry(f"{width}x{height}+{x}+{y}")

        # 创建主框架
        main_frame = ttk.Frame(failed_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 添加统计信息
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        total_size = sum(os.path.getsize(info['file_path']) for info in self.failed_files if os.path.exists(info['file_path']))
        total_size_str = self.get_file_size_str_from_bytes(total_size)

        info_text = f"共 {len(self.failed_files)} 个失败文件，总大小约 {total_size_str}"
        ttk.Label(info_frame, text=info_text, foreground="gray").pack(anchor=tk.W)

        # 创建树形视图框架
        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建树形视图
        columns = ('文件名', '目录', '文件大小', '失败原因', '失败时间')
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=20)

        # 设置列标题和宽度
        tree.heading('文件名', text='文件名')
        tree.column('文件名', width=200)

        tree.heading('目录', text='目录')
        tree.column('目录', width=300)

        tree.heading('文件大小', text='文件大小')
        tree.column('文件大小', width=100)

        tree.heading('失败原因', text='失败原因')
        tree.column('失败原因', width=200)

        tree.heading('失败时间', text='失败时间')
        tree.column('失败时间', width=150)

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=tree.xview)
        tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 使用grid布局树形视图和滚动条
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)

        # 填充数据
        for failed_info in self.failed_files:
            tree.insert('', tk.END, values=(
                failed_info['file_name'],
                failed_info['directory'],
                failed_info['file_size'],
                failed_info['reason'],
                failed_info['timestamp']
            ))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 添加按钮
        def copy_selected():
            """复制选中的文件路径"""
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请先选择要复制的文件")
                return

            paths = []
            for item in selection:
                values = tree.item(item, 'values')
                file_name = values[0]
                directory = values[1]
                full_path = os.path.join(directory, file_name)
                paths.append(full_path)

            path_text = '\n'.join(paths)
            failed_window.clipboard_clear()
            failed_window.clipboard_append(path_text)
            messagebox.showinfo("成功", f"已复制 {len(paths)} 个文件路径到剪贴板")

        def open_file_location():
            """打开选中文件的所在位置"""
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请先选择要打开位置的文件")
                return

            # 只打开第一个选中文件的位置
            item = selection[0]
            values = tree.item(item, 'values')
            directory = values[1]

            try:
                import subprocess
                subprocess.Popen(f'explorer "{directory}"')
            except Exception as e:
                messagebox.showerror("错误", f"无法打开文件夹:\n{e}")

        def retry_selected():
            """重新尝试解压选中的文件"""
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请先选择要重新尝试的文件")
                return

            if messagebox.askyesno("确认", f"确定要重新尝试解压选中的 {len(selection)} 个文件吗？\n\n注意：这将启动新的解压任务"):
                # 收集要重新尝试的文件路径
                retry_files = []
                for item in selection:
                    values = tree.item(item, 'values')
                    file_name = values[0]
                    directory = values[1]
                    full_path = os.path.join(directory, file_name)
                    retry_files.append(full_path)

                # 关闭当前窗口
                failed_window.destroy()

                # 这里可以添加重新尝试解压的逻辑
                # 暂时只显示提示
                messagebox.showinfo("提示", f"已选择 {len(retry_files)} 个文件进行重新尝试\n\n功能开发中，请手动重新解压这些文件")

        def export_list():
            """导出失败文件列表"""
            from tkinter import filedialog
            file_path = filedialog.asksaveasfilename(
                title="保存失败文件列表",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if file_path:
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(f"解压失败文件列表 - 生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("=" * 80 + "\n\n")
                        for i, failed_info in enumerate(self.failed_files, 1):
                            f.write(f"{i}. 文件名: {failed_info['file_name']}\n")
                            f.write(f"   完整路径: {failed_info['file_path']}\n")
                            f.write(f"   文件大小: {failed_info['file_size']}\n")
                            f.write(f"   失败原因: {failed_info['reason']}\n")
                            f.write(f"   失败时间: {failed_info['timestamp']}\n")
                            f.write("-" * 60 + "\n")
                    messagebox.showinfo("成功", f"失败文件列表已保存到:\n{file_path}")
                except Exception as e:
                    messagebox.showerror("错误", f"保存文件列表失败:\n{e}")

        ttk.Button(button_frame, text="复制选中路径", command=copy_selected).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="打开文件位置", command=open_file_location).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="重新尝试", command=retry_selected).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="导出列表", command=export_list).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=failed_window.destroy).pack(side=tk.RIGHT)

    def show_skipped_files(self):
        """显示跳过文件的详细信息"""
        if not self.skipped_files:
            messagebox.showinfo("信息", "当前没有跳过的文件")
            return

        # 创建跳过文件查看窗口
        skipped_window = tk.Toplevel(self.root)
        skipped_window.title(f"跳过文件详情 ({len(self.skipped_files)} 个)")
        skipped_window.geometry("1000x600")
        skipped_window.transient(self.root)
        skipped_window.grab_set()

        # 居中显示
        skipped_window.update_idletasks()
        x = (skipped_window.winfo_screenwidth() // 2) - (skipped_window.winfo_width() // 2)
        y = (skipped_window.winfo_screenheight() // 2) - (skipped_window.winfo_height() // 2)
        skipped_window.geometry(f"+{x}+{y}")

        # 主框架
        main_frame = ttk.Frame(skipped_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 说明标签
        info_label = ttk.Label(main_frame, text=f"以下是跳过的 {len(self.skipped_files)} 个文件的详细信息：")
        info_label.pack(anchor=tk.W, pady=(0, 10))

        # 创建表格
        columns = ('文件名', '目录', '文件大小', '跳过原因', '时间')
        tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=20)

        # 设置列标题和宽度
        tree.heading('文件名', text='文件名')
        tree.heading('目录', text='目录')
        tree.heading('文件大小', text='文件大小')
        tree.heading('跳过原因', text='跳过原因')
        tree.heading('时间', text='时间')

        tree.column('文件名', width=200)
        tree.column('目录', width=300)
        tree.column('文件大小', width=100)
        tree.column('跳过原因', width=200)
        tree.column('时间', width=150)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 填充数据
        for skipped_info in self.skipped_files:
            tree.insert('', tk.END, values=(
                skipped_info['file_name'],
                skipped_info['directory'],
                skipped_info['file_size'],
                skipped_info['reason'],
                skipped_info['timestamp']
            ))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 添加按钮
        def copy_selected():
            """复制选中的文件路径"""
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请先选择要复制的文件")
                return

            paths = []
            for item in selection:
                values = tree.item(item, 'values')
                file_name = values[0]
                directory = values[1]
                full_path = os.path.join(directory, file_name)
                paths.append(full_path)

            path_text = '\n'.join(paths)
            skipped_window.clipboard_clear()
            skipped_window.clipboard_append(path_text)
            messagebox.showinfo("成功", f"已复制 {len(paths)} 个文件路径到剪贴板")

        def open_file_location():
            """打开选中文件的所在位置"""
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请先选择要打开位置的文件")
                return

            # 只打开第一个选中文件的位置
            item = selection[0]
            values = tree.item(item, 'values')
            directory = values[1]

            try:
                import subprocess
                subprocess.Popen(f'explorer "{directory}"')
            except Exception as e:
                messagebox.showerror("错误", f"无法打开文件夹:\n{e}")

        def export_list():
            """导出跳过文件列表"""
            from tkinter import filedialog
            file_path = filedialog.asksaveasfilename(
                title="保存跳过文件列表",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if file_path:
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(f"跳过文件列表 - 生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("=" * 80 + "\n\n")
                        for i, skipped_info in enumerate(self.skipped_files, 1):
                            f.write(f"{i}. 文件名: {skipped_info['file_name']}\n")
                            f.write(f"   完整路径: {skipped_info['file_path']}\n")
                            f.write(f"   文件大小: {skipped_info['file_size']}\n")
                            f.write(f"   跳过原因: {skipped_info['reason']}\n")
                            f.write(f"   跳过时间: {skipped_info['timestamp']}\n")
                            f.write("-" * 60 + "\n")
                    messagebox.showinfo("成功", f"跳过文件列表已保存到:\n{file_path}")
                except Exception as e:
                    messagebox.showerror("错误", f"保存文件列表失败:\n{e}")

        ttk.Button(button_frame, text="复制选中路径", command=copy_selected).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="打开文件位置", command=open_file_location).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="导出列表", command=export_list).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=skipped_window.destroy).pack(side=tk.RIGHT)

    def clear_output(self):
        """清空输出日志"""
        self.log_text.delete(1.0, tk.END)

    def view_logs(self):
        """查看解压日志"""
        folder_path = self.folder_var.get().strip()
        if not folder_path:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        log_file = os.path.join(folder_path, 'unzip_log.json')
        if not os.path.exists(log_file):
            messagebox.showinfo("信息", "未找到解压日志文件")
            return

        # 创建日志查看窗口
        self.show_log_window(log_file)

    def show_log_window(self, log_file):
        """显示日志查看窗口"""
        log_window = tk.Toplevel(self.root)
        log_window.title("解压日志查看")
        log_window.geometry("800x600")

        # 设置窗口居中
        log_window.update_idletasks()
        width = log_window.winfo_width()
        height = log_window.winfo_height()
        x = (log_window.winfo_screenwidth() // 2) - (width // 2)
        y = (log_window.winfo_screenheight() // 2) - (height // 2)
        log_window.geometry(f"{width}x{height}+{x}+{y}")

        # 创建树形视图
        frame = ttk.Frame(log_window, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)

        # 创建Treeview
        columns = ('文件名', '原始路径', '解压位置', '使用密码', '解压时间')
        tree = ttk.Treeview(frame, columns=columns, show='headings', height=20)

        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 读取并显示日志数据
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            for entry in log_data:
                tree.insert('', tk.END, values=(
                    entry.get('file_name', ''),
                    entry.get('original_path', ''),
                    entry.get('extract_to', ''),
                    entry.get('password_used', '无密码'),
                    entry.get('timestamp', '')
                ))

        except Exception as e:
            messagebox.showerror("错误", f"读取日志文件失败：{e}")

        # 添加关闭按钮
        ttk.Button(frame, text="关闭", command=log_window.destroy).pack(pady=10)

    def edit_password_dict(self):
        """编辑密码字典文件"""
        folder_path = self.folder_var.get().strip()
        if not folder_path:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        if not os.path.exists(folder_path):
            messagebox.showerror("错误", "选择的文件夹不存在")
            return

        password_dict_path = os.path.join(folder_path, 'passdict.txt')

        # 创建密码字典编辑窗口
        self.show_password_dict_editor(password_dict_path)

    def show_password_dict_editor(self, dict_path):
        """显示密码字典编辑窗口"""
        editor_window = tk.Toplevel(self.root)
        editor_window.title("密码字典编辑器")
        editor_window.geometry("600x500")

        # 设置窗口居中
        editor_window.update_idletasks()
        width = editor_window.winfo_width()
        height = editor_window.winfo_height()
        x = (editor_window.winfo_screenwidth() // 2) - (width // 2)
        y = (editor_window.winfo_screenheight() // 2) - (height // 2)
        editor_window.geometry(f"{width}x{height}+{x}+{y}")

        # 创建主框架
        main_frame = ttk.Frame(editor_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 文件路径显示
        path_frame = ttk.Frame(main_frame)
        path_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(path_frame, text="文件路径:").pack(side=tk.LEFT)
        ttk.Label(path_frame, text=dict_path, foreground="blue").pack(side=tk.LEFT, padx=(5, 0))

        # 说明文字
        info_frame = ttk.LabelFrame(main_frame, text="使用说明", padding="5")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        info_text = """• 每行输入一个密码
• 程序会按从上到下的顺序尝试密码
• 以 # 开头的行为注释，会被忽略
• 建议将成功率高的密码放在前面以提高效率"""

        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).pack(anchor=tk.W)

        # 文本编辑区域
        edit_frame = ttk.LabelFrame(main_frame, text="密码列表", padding="5")
        edit_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建文本编辑器
        text_editor = scrolledtext.ScrolledText(edit_frame, height=15, wrap=tk.WORD, font=('Consolas', 10))
        text_editor.pack(fill=tk.BOTH, expand=True)

        # 读取现有内容
        try:
            if os.path.exists(dict_path):
                # 尝试多种编码格式读取文件
                content = self.read_file_with_multiple_encodings(dict_path)
                if content is not None:
                    text_editor.insert(tk.END, content)
                else:
                    text_editor.insert(tk.END, "# 无法读取现有文件，请手动输入密码\n")
            else:
                # 如果文件不存在，提供默认模板
                default_content = """# 密码字典文件
# 每行一个密码，程序会按顺序尝试
# 以 # 开头的行为注释
# 请在下方添加您的密码

"""
                text_editor.insert(tk.END, default_content)
        except Exception as e:
            messagebox.showerror("错误", f"读取密码字典文件失败：{e}")
            text_editor.insert(tk.END, "# 无法读取现有文件，请手动输入密码\n")

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def save_dict():
            """保存密码字典"""
            try:
                content = text_editor.get(1.0, tk.END)
                # 确保目录存在
                os.makedirs(os.path.dirname(dict_path), exist_ok=True)

                # 使用ANSI编码保存，与原程序保持一致
                with open(dict_path, 'w', encoding='ansi') as f:
                    f.write(content)

                messagebox.showinfo("成功", f"密码字典已保存到：\n{dict_path}")
                self.log_message(f"密码字典已保存：{dict_path}")

            except UnicodeEncodeError:
                # 如果ANSI编码失败，尝试UTF-8
                try:
                    with open(dict_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    messagebox.showinfo("成功", f"密码字典已保存到（UTF-8编码）：\n{dict_path}")
                    self.log_message(f"密码字典已保存（UTF-8编码）：{dict_path}")
                except Exception as e:
                    messagebox.showerror("错误", f"保存密码字典失败：{e}")
            except Exception as e:
                messagebox.showerror("错误", f"保存密码字典失败：{e}")



        def clear_dict():
            """清空内容"""
            if messagebox.askyesno("确认", "确定要清空所有内容吗？"):
                text_editor.delete(1.0, tk.END)

        # 按钮
        ttk.Button(button_frame, text="保存", command=save_dict).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清空", command=clear_dict).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=editor_window.destroy).pack(side=tk.RIGHT)

        # 统计信息
        def update_stats():
            """更新统计信息"""
            content = text_editor.get(1.0, tk.END)
            lines = content.split('\n')
            total_lines = len([line for line in lines if line.strip()])
            password_lines = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
            comment_lines = len([line for line in lines if line.strip().startswith('#')])

            stats_text = f"总行数: {total_lines} | 密码: {password_lines} | 注释: {comment_lines}"
            stats_label.config(text=stats_text)

        stats_frame = ttk.Frame(main_frame)
        stats_frame.pack(fill=tk.X, pady=(5, 0))

        stats_label = ttk.Label(stats_frame, text="", foreground="gray")
        stats_label.pack(side=tk.LEFT)

        # 绑定文本变化事件
        def on_text_change(event=None):
            editor_window.after_idle(update_stats)

        text_editor.bind('<KeyRelease>', on_text_change)
        text_editor.bind('<Button-1>', on_text_change)

        # 初始统计
        update_stats()

    def read_file_with_multiple_encodings(self, file_path):
        """使用多种编码尝试读取文件"""
        # 尝试多种编码格式，优先使用ANSI编码
        encodings = ['ansi', 'gbk', 'gb2312', 'utf-8', 'latin-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    self.log_message(f"使用 {encoding} 编码成功读取密码字典文件")
                    return content
            except UnicodeDecodeError:
                self.log_message(f"使用 {encoding} 编码读取失败，尝试下一种编码...")
                continue
            except Exception as e:
                self.log_message(f"读取文件时出错：{e}", "ERROR")
                continue

        self.log_message("所有编码格式都无法读取密码字典文件", "ERROR")
        return None

    def normalize_extensions(self):
        """规范文件后缀"""
        folder_path = self.folder_var.get().strip()
        if not folder_path:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        if not os.path.exists(folder_path):
            messagebox.showerror("错误", "选择的文件夹不存在")
            return

        # 直接开始规范化，不显示确认对话框

        # 清空之前的重命名历史记录
        self.rename_history.clear()
        self.undo_button.config(state="disabled")

        # 设置处理状态
        self.is_normalizing = True

        # 在新线程中执行规范化
        self.normalization_thread = threading.Thread(target=self.normalize_extensions_thread, args=(folder_path,))
        self.normalization_thread.daemon = True
        self.normalization_thread.start()

    def normalize_extensions_thread(self, folder_path):
        """在线程中执行文件后缀规范化"""
        try:
            self.log_message(f"开始规范化文件后缀: {folder_path}")
            self.update_progress("正在扫描文件...")

            processed_count = 0

            total_files = 0
            # 先统计总文件数
            for root, _, files in os.walk(folder_path):
                total_files += len(files)

            self.log_message(f"找到 {total_files} 个文件需要检查")

            current_file = 0
            for root, _, files in os.walk(folder_path):
                if not self.is_normalizing:
                    break

                for filename in files:
                    if not self.is_normalizing:
                        break

                    current_file += 1
                    file_path = os.path.join(root, filename)

                    # 在进度条显示当前扫描的文件
                    self.update_progress(f"扫描文件 ({current_file}/{total_files}): {os.path.basename(filename)}")

                    # 跳过正在下载的文件
                    if filename.endswith('.downloading'):
                        self.log_message(f"跳过正在下载的文件：{filename}")
                        continue

                    # 检查文件是否存在且可访问
                    if not os.path.exists(file_path):
                        continue

                    # 处理扩展名
                    new_file_path, ext_changed = self.process_special_extensions(file_path, interactive=True)

                    if ext_changed:
                        try:
                            os.rename(file_path, new_file_path)
                            # 记录重命名操作到历史记录
                            self.rename_history.append((file_path, new_file_path))
                            # 只记录实际被规范化的文件
                            self.log_message(f"规范化扩展名: {os.path.basename(file_path)} -> {os.path.basename(new_file_path)}", "SUCCESS")
                            processed_count += 1
                        except Exception as e:
                            self.log_message(f"规范化扩展名时出错: {file_path} -> {new_file_path}. 错误: {str(e)}", "ERROR")

            self.log_message(f"文件后缀规范化完成！共处理了 {processed_count} 个文件", "SUCCESS")

            # 如果有重命名操作，启用撤销按钮
            if self.rename_history:
                self.undo_button.config(state="normal")
                self.log_message(f"可以撤销 {len(self.rename_history)} 个重命名操作", "INFO")

        except Exception as e:
            self.log_message(f"规范化过程出现异常：{e}", "ERROR")
        finally:
            self.is_normalizing = False
            self.update_progress("就绪")

    def undo_rename(self):
        """撤销重命名操作"""
        if not self.rename_history:
            messagebox.showinfo("提示", "没有可撤销的重命名操作")
            return

        # 显示确认对话框
        count = len(self.rename_history)
        if not messagebox.askyesno("确认撤销", f"确定要撤销最近的 {count} 个重命名操作吗？\n\n此操作将把文件名恢复到重命名前的状态。"):
            return

        success_count = 0
        failed_count = 0
        failed_files = []

        # 逆序撤销操作（最后的操作先撤销）
        for old_path, new_path in reversed(self.rename_history):
            try:
                # 检查新文件是否存在
                if not os.path.exists(new_path):
                    self.log_message(f"撤销失败：文件不存在 {os.path.basename(new_path)}", "ERROR")
                    failed_count += 1
                    failed_files.append(os.path.basename(new_path))
                    continue

                # 检查原文件名是否已被占用
                if os.path.exists(old_path):
                    self.log_message(f"撤销失败：原文件名已被占用 {os.path.basename(old_path)}", "ERROR")
                    failed_count += 1
                    failed_files.append(os.path.basename(new_path))
                    continue

                # 执行撤销重命名
                os.rename(new_path, old_path)
                self.log_message(f"撤销重命名: {os.path.basename(new_path)} -> {os.path.basename(old_path)}", "SUCCESS")
                success_count += 1

            except Exception as e:
                self.log_message(f"撤销重命名失败: {os.path.basename(new_path)} -> {os.path.basename(old_path)}. 错误: {str(e)}", "ERROR")
                failed_count += 1
                failed_files.append(os.path.basename(new_path))

        # 清空历史记录并禁用撤销按钮
        self.rename_history.clear()
        self.undo_button.config(state="disabled")

        # 显示撤销结果
        if failed_count == 0:
            self.log_message(f"撤销完成！成功撤销了 {success_count} 个重命名操作", "SUCCESS")
            messagebox.showinfo("撤销完成", f"成功撤销了 {success_count} 个重命名操作")
        else:
            self.log_message(f"撤销完成！成功: {success_count}, 失败: {failed_count}", "WARNING")
            failed_list = "\n".join(failed_files[:10])  # 最多显示10个失败的文件
            if len(failed_files) > 10:
                failed_list += f"\n... 还有 {len(failed_files) - 10} 个文件"
            messagebox.showwarning("撤销完成", f"撤销结果：\n成功: {success_count} 个\n失败: {failed_count} 个\n\n失败的文件：\n{failed_list}")

    def process_special_extensions(self, file_path, interactive=False):
        """处理特殊的文件扩展名和大文件"""
        filename, ext = os.path.splitext(file_path)
        new_path = file_path
        changed = False

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return new_path, changed

        try:
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)

            # 如果文件没有扩展名且大于100MB，添加.7z扩展名
            if not ext and file_size > 100 * 1024 * 1024:
                new_path = filename + '.7z'
                changed = True
                self.log_message(f"大文件无扩展名，添加.7z: {os.path.basename(file_path)}")

            # 如果扩展名中有汉字，移除汉字
            elif any('\u4e00' <= char <= '\u9fff' for char in ext):
                new_ext = ''.join(char for char in ext if not ('\u4e00' <= char <= '\u9fff'))
                new_path = filename + new_ext
                changed = True
                self.log_message(f"移除扩展名中的汉字: {ext} -> {new_ext}")

            # 检查文件名中是否包含汉字扩展名（如 视频1.7删z.001）
            elif '.' in filename:
                # 分析整个文件名的所有部分
                full_basename = os.path.basename(file_path)
                parts = full_basename.split('.')

                # 检查是否有包含汉字的部分（除了第一个文件名部分）
                has_chinese_extension = False
                for i, part in enumerate(parts[1:], 1):  # 跳过第一个部分（文件名）
                    if any('\u4e00' <= char <= '\u9fff' for char in part):
                        has_chinese_extension = True
                        break

                if has_chinese_extension:
                    # 移除所有扩展名部分中的汉字
                    new_parts = [parts[0]]  # 保留文件名部分
                    for part in parts[1:]:
                        # 移除汉字字符
                        clean_part = ''.join(char for char in part if not ('\u4e00' <= char <= '\u9fff'))
                        if clean_part:  # 如果清理后还有内容
                            new_parts.append(clean_part)

                    new_filename = '.'.join(new_parts)
                    new_path = os.path.join(os.path.dirname(file_path), new_filename)
                    changed = True
                    self.log_message(f"移除文件名中扩展名的汉字: {full_basename} -> {new_filename}")

            # 将大于300MB的PDF、DOC、TXT、MP3文件重命名为.zip
            elif ext.lower() in ['.pdf', '.doc', '.docx', '.txt', '.gif', '.mp3'] and file_size > 300 * 1024 * 1024:
                new_path = filename + '.zip'
                changed = True
                self.log_message(f"大文件扩展名标准化: {ext} -> .zip (文件大小: {file_size_mb:.1f}MB)")

            # 将扩展名为.7、.7zz或.z的文件更改为.7z
            elif ext.lower() in ['.7', '.7zz', '.z']:
                new_path = filename + '.7z'
                changed = True
                self.log_message(f"扩展名标准化: {ext} -> .7z")

            # 检查是否为四段式扩展名（如 L-叛逆.7z.005.avi），要求大于50MB
            elif interactive and file_size > 50 * 1024 * 1024:
                def has_four_part_extension(filepath):
                    """检查是否为四段式扩展名"""
                    basename = os.path.basename(filepath)
                    parts = basename.split('.')
                    # 如果有4个或更多部分（文件名 + 3个扩展名），且最后三个都是扩展名格式
                    if len(parts) >= 4:
                        # 检查最后三个部分是否都像扩展名（长度2-4个字符）
                        last_three = parts[-3:]
                        for part in last_three:
                            if len(part) < 2 or len(part) > 4:
                                return False
                        return True
                    return False

                # 如果是四段式扩展名，直接判定为未知扩展名（要求大于50MB）
                if has_four_part_extension(file_path):
                    file_display_name = os.path.basename(file_path)
                    size_display = f"{file_size_mb:.1f}MB"

                    # 弹出处理菜单对话框
                    result = self.show_large_file_dialog(file_display_name, size_display, ext)

                    if result == "rename_7z":
                        new_path = filename + '.7z'
                        changed = True
                        self.log_message(f"四段式扩展名重命名为.7z：{file_display_name} -> {os.path.basename(new_path)}")
                    elif result == "rename_zip":
                        new_path = filename + '.zip'
                        changed = True
                        self.log_message(f"四段式扩展名重命名为.zip：{file_display_name} -> {os.path.basename(new_path)}")
                    elif result and result.startswith("manual:"):
                        # 手动重命名
                        new_filename = result[7:]  # 去掉 "manual:" 前缀
                        new_path = os.path.join(os.path.dirname(file_path), new_filename)
                        changed = True
                        self.log_message(f"四段式扩展名手动重命名：{file_display_name} -> {new_filename}")
                    else:
                        self.log_message(f"用户跳过四段式扩展名：{file_display_name}")
                        self.record_skipped_file(file_path, "用户跳过四段式扩展名处理")

                # 如果不是四段式扩展名，检查是否为大文件的未知扩展名
                elif file_size > 500 * 1024 * 1024:
                    # 定义已知的扩展名列表
                    known_extensions = {
                        # 压缩文件
                        '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.z',
                        # 视频文件
                        '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v',
                        # 音频文件
                        '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
                        # 图片文件
                        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.svg',
                        # 文档文件
                        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
                        # 程序文件
                        '.exe', '.msi', '.deb', '.rpm', '.dmg', '.pkg', '.app',
                        # 其他常见文件
                        '.iso', '.img', '.bin', '.dat', '.log', '.cfg', '.ini'
                    }

                    # 检查是否为分卷压缩文件的扩展名
                    def is_volume_extension(extension):
                        """检查是否为分卷压缩文件的扩展名"""
                        if not extension:
                            return False
                        ext_lower = extension.lower()
                        # 检查 .001, .002, .003 等数字格式
                        if re.match(r'^\.\d{3}$', ext_lower):
                            return True
                        # 检查 .z01, .z02, .z03 等格式
                        if re.match(r'^\.z\d{2}$', ext_lower):
                            return True
                        return False

                    # 检查是否为分卷压缩文件（如 .001, .002, .003 等）
                    is_volume_file = False
                    if ext and re.match(r'^\.\d{3}$', ext):  # 匹配 .001, .002 等格式
                        # 检查文件名是否以常见压缩格式结尾
                        base_filename = os.path.basename(filename).lower()
                        if any(base_filename.endswith(archive_ext) for archive_ext in ['.7z', '.zip', '.rar', '.tar']):
                            is_volume_file = True
                            self.log_message(f"识别为分卷压缩文件: {os.path.basename(file_path)}")

                    # 额外检查：处理 .7z.001, .zip.001, .rar.001 等格式
                    if not is_volume_file and ext and re.match(r'^\.\d{3}$', ext):
                        # 检查是否为 xxx.7z.001 格式
                        if filename.lower().endswith(('.7z', '.zip', '.rar', '.tar')):
                            is_volume_file = True
                            self.log_message(f"识别为分卷压缩文件 (多扩展名格式): {os.path.basename(file_path)}")

                    # 检查其他分卷格式：.z01, .z02 等
                    if not is_volume_file and ext and re.match(r'^\.z\d{2}$', ext):
                        is_volume_file = True
                        self.log_message(f"识别为分卷压缩文件 (.z## 格式): {os.path.basename(file_path)}")

                    # 检查是否为三段式扩展名（如 .7z.003.zip）
                    def has_triple_extension(filepath):
                        """检查是否为三段式扩展名"""
                        basename = os.path.basename(filepath)
                        parts = basename.split('.')
                        # 如果有4个或更多部分（文件名 + 3个扩展名），且最后三个都是扩展名格式
                        if len(parts) >= 4:
                            # 检查最后三个部分是否都像扩展名（长度2-4个字符，且不全是数字）
                            last_three = parts[-3:]
                            for part in last_three:
                                if len(part) < 2 or len(part) > 4:
                                    return False
                            return True
                        return False

                    is_triple_extension = has_triple_extension(file_path)

                    # 检查是否为未知扩展名或空扩展名（排除分卷压缩文件）
                    # 三段式扩展名需要大于50MB才识别为未知扩展名
                    is_unknown_extension = (not ext or ext.lower() not in known_extensions)
                    is_large_triple_extension = (is_triple_extension and file_size > 50 * 1024 * 1024)

                    if (not is_volume_file and not is_volume_extension(ext) and
                        (is_unknown_extension or is_large_triple_extension)):
                        file_display_name = os.path.basename(file_path)
                        size_display = f"{file_size_mb:.1f}MB"

                        # 弹出处理菜单对话框
                        result = self.show_large_file_dialog(file_display_name, size_display, ext)

                        if result == "rename_7z":
                            new_path = filename + '.7z'
                            changed = True
                            self.log_message(f"用户选择重命名为.7z：{file_display_name} -> {os.path.basename(new_path)}")
                        elif result == "rename_zip":
                            new_path = filename + '.zip'
                            changed = True
                            self.log_message(f"用户选择重命名为.zip：{file_display_name} -> {os.path.basename(new_path)}")
                        elif result and result.startswith("manual:"):
                            # 手动重命名
                            new_filename = result[7:]  # 去掉 "manual:" 前缀
                            new_path = os.path.join(os.path.dirname(file_path), new_filename)
                            changed = True
                            self.log_message(f"用户手动重命名：{file_display_name} -> {new_filename}")
                        else:
                            self.log_message(f"用户跳过：{file_display_name}")
                            self.record_skipped_file(file_path, "用户跳过未知扩展名处理")

        except (OSError, IOError) as e:
            self.log_message(f"获取文件大小时出错: {file_path}. 错误: {str(e)}", "ERROR")
            return new_path, changed

        return new_path, changed

    def show_large_file_dialog(self, file_display_name, size_display, ext):
        """显示大文件处理选项对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("大文件扩展名处理")
        dialog.geometry("550x450")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        result = None

        # 主框架
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 文件信息
        info_frame = ttk.LabelFrame(main_frame, text="文件信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(info_frame, text=f"文件名：{file_display_name}", font=('', 9, 'bold')).pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"文件大小：{size_display}", font=('', 9)).pack(anchor=tk.W, pady=(5, 0))
        ttk.Label(info_frame, text=f"当前扩展名：{ext if ext else '(无扩展名)'}", font=('', 9)).pack(anchor=tk.W, pady=(5, 0))

        # 说明文字
        desc_frame = ttk.Frame(main_frame)
        desc_frame.pack(fill=tk.X, pady=(0, 15))

        desc_text = """此文件大于500MB且扩展名未知或不在常见处理范围内。
请选择处理方式："""
        ttk.Label(desc_frame, text=desc_text, font=('', 9)).pack(anchor=tk.W)

        # 选项框架
        options_frame = ttk.LabelFrame(main_frame, text="处理选项", padding="10")
        options_frame.pack(fill=tk.X, pady=(0, 15))

        # 选项变量
        option_var = tk.StringVar(value="rename_7z")

        # 预设选项
        ttk.Radiobutton(options_frame, text="重命名为 .7z（推荐）",
                       variable=option_var, value="rename_7z").pack(anchor=tk.W, pady=2)
        ttk.Radiobutton(options_frame, text="重命名为 .zip",
                       variable=option_var, value="rename_zip").pack(anchor=tk.W, pady=2)

        # 手动重命名选项
        manual_frame = ttk.Frame(options_frame)
        manual_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Radiobutton(manual_frame, text="手动重命名文件：",
                       variable=option_var, value="manual").pack(anchor=tk.W)

        # 显示原文件名
        original_name_frame = ttk.Frame(manual_frame)
        original_name_frame.pack(fill=tk.X, pady=(5, 0), padx=(20, 0))

        ttk.Label(original_name_frame, text="原文件名：", font=('', 8)).pack(side=tk.LEFT)
        ttk.Label(original_name_frame, text=file_display_name, font=('', 8, 'bold')).pack(side=tk.LEFT, padx=(5, 0))

        # 新文件名输入
        new_name_frame = ttk.Frame(manual_frame)
        new_name_frame.pack(fill=tk.X, pady=(5, 0), padx=(20, 0))

        ttk.Label(new_name_frame, text="新文件名：", font=('', 8)).pack(side=tk.LEFT)
        manual_entry = ttk.Entry(new_name_frame, width=30)
        manual_entry.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)

        # 默认显示原文件名但改为.7z扩展名
        base_name = os.path.splitext(file_display_name)[0]
        manual_entry.insert(0, base_name + ".7z")

        # 跳过选项
        ttk.Radiobutton(options_frame, text="跳过此文件（不修改）",
                       variable=option_var, value="skip").pack(anchor=tk.W, pady=(10, 2))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def on_confirm():
            nonlocal result
            selected = option_var.get()
            if selected == "manual":
                new_filename = manual_entry.get().strip()
                if not new_filename:
                    messagebox.showwarning("警告", "请输入新文件名")
                    return
                # 验证文件名是否合法
                if any(char in new_filename for char in '<>:"/\\|?*'):
                    messagebox.showwarning("警告", "文件名包含非法字符：< > : \" / \\ | ? *")
                    return
                result = f"manual:{new_filename}"
            else:
                result = selected
            dialog.destroy()

        def on_cancel():
            nonlocal result
            result = "skip"
            dialog.destroy()

        ttk.Button(button_frame, text="确定", command=on_confirm).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=on_cancel).pack(side=tk.RIGHT)

        # 等待对话框关闭
        dialog.wait_window()
        return result

    def log_successful_extraction(self, file_path, extract_to, password_used=None):
        """记录成功解压的文件信息"""
        log_file = os.path.join(os.path.dirname(extract_to), 'unzip_log.json')
        log_entry = {
            'file_name': os.path.basename(file_path),
            'original_path': file_path,
            'extract_to': extract_to,
            'password_used': password_used,
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        try:
            # 读取现有日志
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
            else:
                log_data = []

            # 添加新记录
            log_data.append(log_entry)

            # 保存日志
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

            self.log_message(f"解压记录已保存到：{log_file}")
        except Exception as e:
            self.log_message(f"保存解压记录时出错：{e}", "ERROR")

    def get_dir_size(self, path):
        """获取目录总大小"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for f in filenames:
                fp = os.path.join(dirpath, f)
                try:
                    total_size += os.path.getsize(fp)
                except OSError:
                    continue
        return total_size

    def is_split_archive_main_file(self, file_path):
        """判断是否是分卷压缩包的主文件（第一个文件）"""
        file_name = os.path.basename(file_path)
        dir_path = os.path.dirname(file_path)

        import re

        # 检查 .part01.rar, .part001.rar 等格式（只有第一个是主文件）
        if re.search(r'\.part0*1\.(rar|zip|7z|tar)$', file_name, re.IGNORECASE):
            return True

        # 检查 .001, .7z.001, .zip.001 等格式
        if re.search(r'\.(rar|zip|7z|tar)\.001$', file_name, re.IGNORECASE):
            return True

        # 检查 .zip 文件（可能是 .z01 系列的主文件）
        if file_name.lower().endswith('.zip'):
            base_name = os.path.splitext(file_name)[0]
            # 检查是否存在对应的 .z01 文件
            z01_file = os.path.join(dir_path, base_name + '.z01')
            if os.path.exists(z01_file):
                return True  # 这是 .z01 系列的主文件
            else:
                # 检查是否存在其他 .zXX 文件
                has_z_files = any(f.startswith(base_name + '.z') and f != file_name
                                 for f in os.listdir(dir_path))
                if not has_z_files:
                    return True  # 这是单个 zip 文件
                else:
                    return False  # 这不是主文件

        # 检查单个文件（非分卷）
        if re.search(r'\.(rar|zip|7z|tar)$', file_name, re.IGNORECASE):
            base_name = os.path.splitext(file_name)[0]
            ext = os.path.splitext(file_name)[1]

            # 检查是否存在分卷文件
            has_split_files = any(
                f.startswith(base_name) and (
                    re.search(r'\.part\d+' + re.escape(ext) + r'$', f, re.IGNORECASE) or
                    re.search(re.escape(ext) + r'\.\d+$', f, re.IGNORECASE)
                ) for f in os.listdir(dir_path)
            )

            if not has_split_files:
                return True  # 这是单个文件

        return False

    def get_all_split_archive_files(self, file_path):
        """获取分卷压缩包的所有相关文件（更安全的版本）"""
        dir_path = os.path.dirname(file_path)
        file_name = os.path.basename(file_path)
        related_files = []

        import re

        # 处理 .part01.rar 格式
        if re.search(r'\.part\d+\.(rar|zip|7z|tar)$', file_name, re.IGNORECASE):
            base_match = re.match(r'(.+)\.part(\d+)\.(rar|zip|7z|tar)$', file_name, re.IGNORECASE)
            if base_match:
                base_name = base_match.group(1)
                ext = base_match.group(3)

                # 首先找到所有可能的分卷文件
                potential_files = []
                for f in os.listdir(dir_path):
                    part_match = re.match(rf'{re.escape(base_name)}\.part(\d+)\.{ext}$', f, re.IGNORECASE)
                    if part_match:
                        part_num = int(part_match.group(1))
                        potential_files.append((part_num, f))

                # 排序并检查连续性
                potential_files.sort()
                if potential_files:
                    # 找到连续的分卷序列
                    consecutive_files = []
                    expected_num = 1
                    for part_num, filename in potential_files:
                        if part_num == expected_num:
                            consecutive_files.append(filename)
                            expected_num += 1
                        elif part_num > expected_num:
                            # 如果跳过了某个序号，停止添加
                            break

                    # 只添加连续的分卷文件
                    for filename in consecutive_files:
                        related_files.append(os.path.join(dir_path, filename))

        # 处理 .7z.001 格式
        elif re.search(r'\.(rar|zip|7z|tar)\.\d+$', file_name, re.IGNORECASE):
            base_match = re.match(r'(.+)\.(rar|zip|7z|tar)\.(\d+)$', file_name, re.IGNORECASE)
            if base_match:
                base_name = base_match.group(1)
                ext = base_match.group(2)
                current_num = int(base_match.group(3))

                # 只处理从 001 开始的分卷
                if current_num == 1:
                    # 找到所有连续的分卷文件
                    num = 1
                    while True:
                        expected_file = f"{base_name}.{ext}.{num:03d}"
                        expected_path = os.path.join(dir_path, expected_file)
                        if os.path.exists(expected_path):
                            related_files.append(expected_path)
                            num += 1
                        else:
                            break

        # 处理 .z01, .z02 格式（主文件是 .zip）
        elif file_name.lower().endswith('.zip'):
            base_name = os.path.splitext(file_name)[0]

            # 检查是否存在 .z01 文件，如果存在才认为是分卷
            z01_file = os.path.join(dir_path, f"{base_name}.z01")
            if os.path.exists(z01_file):
                # 包含主 .zip 文件
                related_files.append(file_path)

                # 查找连续的 .z01, .z02, .z03... 文件
                num = 1
                while True:
                    z_file = os.path.join(dir_path, f"{base_name}.z{num:02d}")
                    if os.path.exists(z_file):
                        related_files.append(z_file)
                        num += 1
                    else:
                        break
            else:
                # 单个 zip 文件
                related_files.append(file_path)

        # 如果没有找到相关文件，说明是单个文件
        if not related_files:
            related_files.append(file_path)

        return related_files

    def delete_split_archive_files(self, file_path):
        """删除分卷压缩文件的所有部分"""
        related_files = self.get_all_split_archive_files(file_path)

        for file_to_delete in related_files:
            try:
                if os.path.exists(file_to_delete):
                    os.remove(file_to_delete)
                    self.log_message(f"已删除分卷文件：{file_to_delete}")
            except Exception as e:
                self.log_message(f"删除文件 {file_to_delete} 时出错：{e}", "ERROR")

    def extract_with_7z(self, file_path, extract_to, password):
        """使用给定密码解压文件"""
        self.log_message(f"尝试使用密码：{password}")

        command = [r'C:\Program Files\7-Zip\7z.exe', 'x', file_path, f'-p{password}', '-aou']
        process = None
        try:
            process = subprocess.Popen(command, cwd=extract_to, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                     universal_newlines=True, creationflags=subprocess.CREATE_NO_WINDOW)
            self.current_process = process

            start_time = time.time()
            last_size = self.get_dir_size(extract_to)
            last_check_time = start_time

            while self.is_processing:
                current_time = time.time()

                # 检查进程是否结束
                if process.poll() is not None:
                    break

                # 如果用户停止了操作，终止进程
                if not self.is_processing:
                    self.log_message("检测到停止信号，终止7z进程...")
                    try:
                        process.terminate()
                        process.wait(timeout=2)
                    except subprocess.TimeoutExpired:
                        process.kill()
                    return False

                # 每10秒检查一次目录大小
                if current_time - last_check_time >= 10:
                    current_size = self.get_dir_size(extract_to)
                    if current_size > last_size:
                        # 目录在增长，重置计时器
                        start_time = current_time
                        last_size = current_size
                    last_check_time = current_time

                # 检查是否超时
                if current_time - start_time > 300:
                    self.log_message("解压操作超时，终止当前尝试", "WARNING")
                    try:
                        process.terminate()
                        process.wait(timeout=2)
                    except subprocess.TimeoutExpired:
                        process.kill()
                    return False

                # 读取并显示输出
                try:
                    line = process.stdout.readline()
                    if line:
                        line_stripped = line.strip()
                        self.log_message(line_stripped)

                        # 检查是否出现密码错误，及时退出
                        if "Wrong password" in line_stripped or "ERROR: Wrong password" in line_stripped:
                            self.log_message("检测到密码错误，停止当前尝试", "WARNING")
                            try:
                                process.terminate()
                                process.wait(timeout=2)
                            except subprocess.TimeoutExpired:
                                process.kill()
                            return False
                except:
                    pass  # 进程可能已经结束

                time.sleep(0.1)

            if process.returncode == 0:
                self.log_message("解压成功！", "SUCCESS")
                # 记录成功解压的文件
                self.log_successful_extraction(file_path, extract_to, password)

                # 删除压缩包文件（包括所有分卷文件）
                self.delete_split_archive_files(file_path)

                return True
            self.log_message("解压失败", "ERROR")
            return False
        except PermissionError as e:
            self.log_message(f"权限错误：{e}，请以管理员身份运行程序", "ERROR")
            return False
        except FileNotFoundError:
            self.log_message("未找到7-Zip程序，请确保已正确安装7-Zip", "ERROR")
            return False
        except Exception as e:
            self.log_message(f"解压过程出错：{e}", "ERROR")
            return False
        finally:
            if process:
                try:
                    process.kill()
                except:
                    pass
            self.current_process = None
        return False

    def try_password_dict(self, file_path, extract_to, password_dict_path):
        """尝试使用密码字典中的密码解压"""
        try:
            with open(password_dict_path, 'r', encoding='ansi') as f:
                for password in f:
                    if not self.is_processing:
                        return False
                    password = password.strip()
                    if not password:
                        continue
                    if self.extract_with_7z(file_path, extract_to, password):
                        return True
        except Exception as e:
            self.log_message(f"读取密码字典时出错：{e}", "ERROR")
        return False

    def extract_without_password(self, file_path, extract_to):
        """尝试不使用密码进行解压缩"""
        command = [r'C:\Program Files\7-Zip\7z.exe', 'x', file_path, '-aou']
        process = None
        try:
            process = subprocess.Popen(command, cwd=extract_to, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                     universal_newlines=True, creationflags=subprocess.CREATE_NO_WINDOW)
            self.current_process = process

            start_time = time.time()
            last_size = self.get_dir_size(extract_to)
            last_check_time = start_time

            while self.is_processing:
                current_time = time.time()

                # 检查进程是否结束
                if process.poll() is not None:
                    break

                # 如果用户停止了操作，终止进程
                if not self.is_processing:
                    self.log_message("检测到停止信号，终止7z进程...")
                    try:
                        process.terminate()
                        process.wait(timeout=2)
                    except subprocess.TimeoutExpired:
                        process.kill()
                    return False

                # 每10秒检查一次目录大小
                if current_time - last_check_time >= 10:
                    current_size = self.get_dir_size(extract_to)
                    if current_size > last_size:
                        # 目录在增长，重置计时器
                        start_time = current_time
                        last_size = current_size
                    last_check_time = current_time

                # 检查是否超时
                if current_time - start_time > 300:
                    self.log_message("解压操作超时，终止当前尝试", "WARNING")
                    try:
                        process.terminate()
                        process.wait(timeout=2)
                    except subprocess.TimeoutExpired:
                        process.kill()
                    return False

                # 读取并显示输出
                try:
                    line = process.stdout.readline()
                    if line:
                        line_stripped = line.strip()
                        self.log_message(line_stripped)

                        # 检查是否出现密码错误，及时退出
                        if "Wrong password" in line_stripped or "ERROR: Wrong password" in line_stripped:
                            self.log_message("检测到密码错误，停止当前尝试", "WARNING")
                            try:
                                process.terminate()
                                process.wait(timeout=2)
                            except subprocess.TimeoutExpired:
                                process.kill()
                            return False
                except:
                    pass  # 进程可能已经结束

                time.sleep(0.1)

            if process.returncode == 0:
                self.log_message("解压成功（无密码）", "SUCCESS")
                # 记录成功解压的文件
                self.log_successful_extraction(file_path, extract_to)
                # 删除压缩包文件（包括所有分卷文件）
                self.delete_split_archive_files(file_path)
                return True
            self.log_message("解压失败（无密码）", "ERROR")
            return False
        except PermissionError as e:
            self.log_message(f"权限错误：{e}，请以管理员身份运行程序", "ERROR")
            return False
        except FileNotFoundError:
            self.log_message("未找到7-Zip程序，请确保已正确安装7-Zip", "ERROR")
            return False
        except Exception as e:
            self.log_message(f"解压过程出错：{e}", "ERROR")
            return False
        finally:
            if process:
                try:
                    process.kill()
                except:
                    pass
            self.current_process = None
        return False

    def get_password(self, folder, original_folder):
        """从当前文件夹及其上级文件夹中查找 password.txt 文件，包括原始选择的文件夹，最多尝试 5 次"""
        retry_count = 0  # 重试计数器
        current_folder = folder

        while current_folder and retry_count < 5:
            password_file = os.path.join(current_folder, 'password.txt')
            if os.path.isfile(password_file):
                self.log_message(f"找到密码文件：{password_file}")
                return self.read_password_from_file(password_file)

            # 如果已经到达原始文件夹，停止搜索
            if current_folder == original_folder:
                break

            current_folder = os.path.dirname(current_folder)  # 移动到上一级目录
            retry_count += 1  # 增加重试次数

        return ''

    def read_password_from_file(self, file_path):
        """从 password.txt 文件中解析格式为 '密码：XXXXXX' 的密码"""
        # 尝试多种编码格式，优先使用ANSI编码
        encodings = ['ansi', 'gbk', 'gb2312', 'utf-8', 'latin-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read().strip()
                    self.log_message(f"使用 {encoding} 编码成功读取密码文件")
                    self.log_message(f"密码文件内容：{content}")

                    for line in content.split('\n'):
                        line = line.strip()
                        if line.startswith("密码："):
                            password = line.split("密码：", 1)[1].strip()
                            # 检查是否表示无密码
                            if password.lower() in ['无', 'none', 'null', '空', '']:
                                self.log_message("密码文件指示该压缩包无密码")
                                return None  # 返回None表示无密码
                            self.log_message(f"从文件中解析到密码：{password}")
                            return password
                        # 也尝试其他可能的格式
                        elif line.startswith("密码:"):
                            password = line.split("密码:", 1)[1].strip()
                            # 检查是否表示无密码
                            if password.lower() in ['无', 'none', 'null', '空', '']:
                                self.log_message("密码文件指示该压缩包无密码")
                                return None  # 返回None表示无密码
                            self.log_message(f"从文件中解析到密码：{password}")
                            return password
                        elif line and not line.startswith("#"):  # 如果是非空行且不是注释，直接作为密码
                            # 检查是否表示无密码
                            if line.lower() in ['无', 'none', 'null', '空']:
                                self.log_message("密码文件指示该压缩包无密码")
                                return None  # 返回None表示无密码
                            self.log_message(f"将整行作为密码：{line}")
                            return line

                    self.log_message("未在密码文件中找到有效密码", "WARNING")
                    return ''

            except UnicodeDecodeError:
                self.log_message(f"使用 {encoding} 编码读取失败，尝试下一种编码...")
                continue
            except Exception as e:
                self.log_message(f"读取密码文件时出错：{e}", "ERROR")
                continue

        self.log_message("所有编码格式都无法读取密码文件", "ERROR")
        return ''

    def get_extract_password(self, folder, original_folder):
        """从当前文件夹及其上级文件夹中查找解压密码文件，支持模糊匹配，最多尝试5次"""
        retry_count = 0
        current_folder = folder
        all_found_passwords = []

        while current_folder and retry_count < 5:
            # 获取当前文件夹中的所有文件
            try:
                files_in_folder = os.listdir(current_folder)
            except (OSError, PermissionError):
                # 如果无法访问文件夹，跳到上级目录
                current_folder = os.path.dirname(current_folder)
                retry_count += 1
                continue

            # 精确匹配的文件名（优先级最高）
            exact_match_files = [
                '解压密码.txt', '解压密码.TXT',
                '密码.txt', '密码.TXT',
                'password_extract.txt', 'extract_password.txt', 'unzip_password.txt'
            ]

            # 先尝试精确匹配
            for password_filename in exact_match_files:
                if password_filename in files_in_folder:
                    password_file = os.path.join(current_folder, password_filename)
                    self.log_message(f"找到解压密码文件（精确匹配）：{password_file}")
                    password = self.read_extract_password_from_file(password_file)
                    if password:
                        return password

            # 模糊匹配（部分匹配）
            fuzzy_patterns = [
                '解压密码', '密码', 'password', 'pass', 'pwd',
                '解压', 'extract', 'unzip', 'unlock'
            ]

            matched_files = []
            for file_name in files_in_folder:
                if file_name.lower().endswith(('.txt', '.text')):
                    file_name_lower = file_name.lower()
                    for pattern in fuzzy_patterns:
                        if pattern.lower() in file_name_lower:
                            matched_files.append(file_name)
                            break

            # 尝试所有匹配的文件
            for matched_file in matched_files:
                password_file = os.path.join(current_folder, matched_file)
                self.log_message(f"找到解压密码文件（模糊匹配）：{password_file}")
                password = self.read_extract_password_from_file(password_file)
                if password:
                    all_found_passwords.append(password)

            # 如果已经到达原始文件夹，停止搜索
            if current_folder == original_folder:
                break

            current_folder = os.path.dirname(current_folder)
            retry_count += 1

        # 如果找到多个密码，返回第一个有效的
        if all_found_passwords:
            return all_found_passwords[0]

        return ''

    def get_all_extract_passwords(self, folder, original_folder):
        """获取所有找到的解压密码，支持多个密码文件"""
        retry_count = 0
        current_folder = folder
        all_passwords = []

        while current_folder and retry_count < 5:
            # 获取当前文件夹中的所有文件
            try:
                files_in_folder = os.listdir(current_folder)
            except (OSError, PermissionError):
                # 如果无法访问文件夹，跳到上级目录
                current_folder = os.path.dirname(current_folder)
                retry_count += 1
                continue

            # 精确匹配的文件名（优先级最高）
            exact_match_files = [
                '解压密码.txt', '解压密码.TXT',
                '密码.txt', '密码.TXT',
                'password_extract.txt', 'extract_password.txt', 'unzip_password.txt'
            ]

            # 先处理精确匹配的文件
            for password_filename in exact_match_files:
                if password_filename in files_in_folder:
                    password_file = os.path.join(current_folder, password_filename)
                    self.log_message(f"找到解压密码文件（精确匹配）：{password_file}")
                    password = self.read_extract_password_from_file(password_file)
                    if password is not None:  # 包括None（无密码指示）和具体密码
                        all_passwords.append(password)

            # 模糊匹配（部分匹配）
            fuzzy_patterns = [
                '解压密码', '密码', 'password', 'pass', 'pwd',
                '解压', 'extract', 'unzip', 'unlock'
            ]

            matched_files = []
            for file_name in files_in_folder:
                # 跳过已经精确匹配的文件
                if file_name in exact_match_files:
                    continue

                if file_name.lower().endswith(('.txt', '.text')):
                    file_name_lower = file_name.lower()
                    for pattern in fuzzy_patterns:
                        if pattern.lower() in file_name_lower:
                            matched_files.append(file_name)
                            break

            # 处理模糊匹配的文件
            for matched_file in matched_files:
                password_file = os.path.join(current_folder, matched_file)
                self.log_message(f"找到解压密码文件（模糊匹配）：{password_file}")
                password = self.read_extract_password_from_file(password_file)
                if password is not None:  # 包括None（无密码指示）和具体密码
                    all_passwords.append(password)

            # 如果已经到达原始文件夹，停止搜索
            if current_folder == original_folder:
                break

            current_folder = os.path.dirname(current_folder)
            retry_count += 1

        # 去重并保持顺序
        unique_passwords = []
        seen = set()
        for pwd in all_passwords:
            if pwd not in seen:
                unique_passwords.append(pwd)
                seen.add(pwd)

        if unique_passwords:
            self.log_message(f"总共找到 {len(unique_passwords)} 个不同的密码进行尝试")

        return unique_passwords

    def read_extract_password_from_file(self, file_path):
        """从解压密码文件中读取密码"""
        # 尝试多种编码格式，优先使用GBK编码处理中文
        encodings = ['gbk', 'gb2312', 'utf-8', 'ansi', 'latin-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read().strip()

                    # 检查是否包含乱码字符（如果包含大量非ASCII且非中文字符，可能是编码错误）
                    if self.contains_garbled_text(content):
                        self.log_message(f"使用 {encoding} 编码读取到乱码，尝试下一种编码...")
                        continue

                    self.log_message(f"使用 {encoding} 编码成功读取解压密码文件")
                    self.log_message(f"解压密码文件内容：{content}")

                    for line in content.split('\n'):
                        line = line.strip()

                        # 支持多种密码格式
                        password_patterns = [
                            "密码：", "密码:", "password:", "Password:", "PASSWORD:",
                            "解压密码：", "解压密码:", "extract password:", "Extract Password:",
                            "unzip password:", "Unzip Password:", "解压码：", "解压码:"
                        ]

                        for pattern in password_patterns:
                            if line.startswith(pattern):
                                password = line.split(pattern, 1)[1].strip()
                                # 检查是否表示无密码
                                if password.lower() in ['无', 'none', 'null', '空', '']:
                                    self.log_message("解压密码文件指示该压缩包无密码")
                                    return None
                                self.log_message(f"从解压密码文件中解析到密码：{password}")
                                return password

                        # 如果是非空行且不是注释，直接作为密码
                        if line and not line.startswith("#") and not line.startswith("//"):
                            # 检查是否表示无密码
                            if line.lower() in ['无', 'none', 'null', '空']:
                                self.log_message("解压密码文件指示该压缩包无密码")
                                return None
                            self.log_message(f"将整行作为解压密码：{line}")
                            return line

                    self.log_message("未在解压密码文件中找到有效密码", "WARNING")
                    return ''

            except UnicodeDecodeError:
                self.log_message(f"使用 {encoding} 编码读取解压密码文件失败，尝试下一种编码...")
                continue
            except Exception as e:
                self.log_message(f"读取解压密码文件时出错：{e}", "ERROR")
                continue

        self.log_message("所有编码格式都无法读取解压密码文件", "ERROR")
        return ''

    def contains_garbled_text(self, text):
        """检测文本是否包含乱码"""
        if not text:
            return False

        # 统计各种字符类型
        ascii_count = 0
        chinese_count = 0
        other_count = 0

        for char in text:
            if ord(char) < 128:  # ASCII字符
                ascii_count += 1
            elif '\u4e00' <= char <= '\u9fff':  # 中文字符
                chinese_count += 1
            else:  # 其他字符
                other_count += 1

        total_chars = len(text)
        if total_chars == 0:
            return False

        # 如果其他字符（可能是乱码）占比超过30%，认为是乱码
        other_ratio = other_count / total_chars

        # 特别检查常见的乱码模式
        garbled_patterns = ['瑙ｅ帇', '瀵嗙爜', '锛�', '鈥�', '鈥�', '鈥�', '锛�']
        for pattern in garbled_patterns:
            if pattern in text:
                return True

        # 检查是否包含大量连续的特殊Unicode字符
        special_unicode_count = 0
        for char in text:
            # 检查是否为常见乱码字符范围
            char_code = ord(char)
            if (0x2000 <= char_code <= 0x206F) or (0x2100 <= char_code <= 0x214F) or (0xFF00 <= char_code <= 0xFFEF):
                special_unicode_count += 1

        special_unicode_ratio = special_unicode_count / total_chars if total_chars > 0 else 0

        return other_ratio > 0.3 or special_unicode_ratio > 0.1


def main():
    """主程序入口"""
    root = tk.Tk()
    app = UnzipGUI(root)

    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap('icon.ico')  # 如果有图标文件可以取消注释
        pass
    except:
        pass

    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    main()
