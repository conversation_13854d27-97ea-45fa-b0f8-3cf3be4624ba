# Windows右键菜单 - 文件名空格替换工具

## 项目概述
这是一个Windows右键菜单程序，用于将选中文件的文件名中的空格替换为下划线（_）。支持单个文件和批量文件处理，具有友好的图形界面和详细的操作反馈。

## 核心功能
- ✅ 将文件名中的空格替换为下划线
- ✅ 支持单个文件处理
- ✅ 支持批量文件处理
- ✅ 图形界面显示处理结果
- ✅ 防止文件覆盖（如果目标文件名已存在）
- ✅ 详细的错误处理和权限检查
- ✅ 一键安装/卸载右键菜单

## 文件结构
```
├── replace_spaces_in_filename.py    # 核心Python脚本
├── replace_spaces_menu.bat          # 批处理调用脚本
├── 快速安装右键菜单.bat             # 一键安装（推荐）
├── 快速卸载右键菜单.bat             # 一键卸载
├── 安装右键菜单.bat                 # 详细安装脚本
├── 卸载右键菜单.bat                 # 详细卸载脚本
├── 演示功能.bat                     # 功能演示脚本
├── install_context_menu.reg         # 静态注册表文件
├── uninstall_context_menu.reg       # 静态卸载文件
├── README_右键菜单安装说明.md       # 详细使用说明
└── 项目说明.md                      # 本文件
```

## 快速开始

### 1. 安装
**方法一（推荐）：**
- 右键点击 `快速安装右键菜单.bat`
- 选择"以管理员身份运行"

**方法二：**
- 右键点击 `安装右键菜单.bat`
- 选择"以管理员身份运行"
- 按照提示完成安装

### 2. 使用
安装完成后：
1. 右键点击任意文件
2. 选择"将文件名空格替换为下划线"
3. 查看处理结果

### 3. 卸载
- 右键点击 `快速卸载右键菜单.bat`
- 选择"以管理员身份运行"

## 技术特点

### Python脚本特性
- 使用pathlib进行路径处理
- tkinter创建图形界面
- 完整的错误处理机制
- 支持命令行参数
- 中文界面友好

### 批处理脚本特性
- 自动检测Python环境
- 支持多种Python命令（python/python3/py）
- 简洁的错误处理

### 注册表集成
- 动态生成注册表文件
- 自适应安装路径
- 安全的安装/卸载机制

## 使用示例

### 处理前
```
我的 文档 文件.txt
photo 2023 01 15.jpg
视频 文件 名称.mp4
```

### 处理后
```
我的_文档_文件.txt
photo_2023_01_15.jpg
视频_文件_名称.mp4
```

## 系统要求
- **操作系统**: Windows 7/8/10/11
- **Python**: 3.6+ （可选，脚本会提示安装）
- **权限**: 管理员权限（用于注册表操作）

## 安全特性
- 文件重命名前检查目标文件是否存在
- 详细的权限检查
- 操作日志和结果反馈
- 可撤销的注册表操作

## 故障排除

### 常见问题
1. **右键菜单没有出现**
   - 确保以管理员身份运行安装脚本
   - 重启文件资源管理器或重启电脑

2. **Python环境问题**
   - 从 python.org 下载安装Python
   - 安装时勾选"Add Python to PATH"

3. **权限错误**
   - 确保对文件有写权限
   - 检查文件是否被其他程序占用

## 开发说明
- 编程语言：Python 3 + Windows Batch
- GUI框架：tkinter
- 注册表操作：Windows Registry
- 编码：UTF-8 with BOM（支持中文）

## 版本信息
- **版本**: 1.0
- **创建日期**: 2025年8月
- **兼容性**: Windows 7/8/10/11
- **许可证**: 开源

## 注意事项
⚠️ **重要提醒**：
- 文件重命名操作不可撤销，请谨慎使用
- 建议先在测试文件上验证功能
- 重要文件请提前备份

## 联系支持
如果遇到问题或需要帮助，请查看 `README_右键菜单安装说明.md` 文件中的详细说明。
